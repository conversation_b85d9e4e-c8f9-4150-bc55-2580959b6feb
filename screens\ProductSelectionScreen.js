import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Animated,
  ScrollView,
  Image,
  SafeAreaView,
  StatusBar
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';



export default function ProductSelectionScreen() {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [cart, setCart] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  useEffect(() => {
    loadProducts();
    loadCart();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  // Product categories
  const productCategories = [
    { id: 'all', name: 'All Products' },
    { id: 'own', name: 'Own Products' },
    { id: 'biscuits', name: 'Biscuits' },
    { id: 'savouries', name: 'Savouries' },
    { id: 'other', name: 'Other Items' }
  ];

  const loadProducts = async () => {
    try {
      setIsLoading(true);

      // Load recipes (own products)
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));
      const recipeData = [];

      for (let key of recipeKeys) {
        const raw = await AsyncStorage.getItem(key);
        const parsed = JSON.parse(raw);

        recipeData.push({
          id: key.replace('recipe_', ''),
          name: key.replace('recipe_', ''),
          price: parsed.sellingPrice ? parseFloat(parsed.sellingPrice) : 0,
          ingredients: parsed.ingredients || [],
          category: parsed.category || 'own',
          image: parsed.image || null,
          popular: parsed.popular || false,
          isRecipe: true
        });
      }

      // Load purchased products
      const purchasedProductsData = await AsyncStorage.getItem('purchased_products');
      const purchasedProducts = purchasedProductsData ? JSON.parse(purchasedProductsData) : [];

      // Combine all products
      const allProducts = [...recipeData, ...purchasedProducts];

      // Sort by name
      allProducts.sort((a, b) => a.name.localeCompare(b.name));

      setProducts(allProducts);
      setCategories(productCategories.map(c => c.id));
    } catch (error) {
      console.error('Failed to load products:', error);
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCart = async () => {
    try {
      const cartData = await AsyncStorage.getItem('current_cart');
      if (cartData) {
        setCart(JSON.parse(cartData));
      }
    } catch (error) {
      console.error('Failed to load cart:', error);
    }
  };

  const saveCart = async (updatedCart) => {
    try {
      await AsyncStorage.setItem('current_cart', JSON.stringify(updatedCart));
    } catch (error) {
      console.error('Failed to save cart:', error);
    }
  };

  const addToCart = (product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === product.id);
      let updatedCart;

      if (existingItem) {
        updatedCart = prevCart.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        updatedCart = [...prevCart, { ...product, quantity: 1 }];
      }

      saveCart(updatedCart);
      return updatedCart;
    });
  };

  const getCartCount = () => {
    return cart.reduce((count, item) => count + item.quantity, 0);
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getCategoryName = (categoryId) => {
    const category = productCategories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Uncategorized';
  };

  const filteredProducts = products.filter(product => {
    // Filter by search query if present
    const matchesSearch = searchQuery
      ? product.name.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    // Filter by category if not 'all'
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Sort products: popular first, then alphabetically
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (a.popular && !b.popular) return -1;
    if (!a.popular && b.popular) return 1;
    return a.name.localeCompare(b.name);
  });

  const renderProductItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.productCard, { backgroundColor: theme.surface }]}
      onPress={() => addToCart(item)}
      activeOpacity={0.7}
    >
      {item.popular && (
        <View style={[styles.popularBadge, { backgroundColor: theme.accent }]}>
          <Text style={styles.popularBadgeText}>Popular</Text>
        </View>
      )}

      <View style={styles.productImageContainer}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.productImage} resizeMode="cover" />
        ) : (
          <View style={[styles.productImagePlaceholder, { backgroundColor: theme.card.background }]}>
            <MaterialIcons name="bakery-dining" size={24} color={theme.text.secondary} />
          </View>
        )}
      </View>

      <Text style={[styles.productName, { color: theme.text.primary }]} numberOfLines={1}>
        {item.name}
      </Text>
      <Text style={[styles.productPrice, { color: theme.success }]}>₹{item.price.toFixed(2)}</Text>

      <View style={styles.productActions}>
        <TouchableOpacity
          style={[styles.quantityButton, { backgroundColor: theme.card.background }]}
          onPress={() => addToCart(item)}
        >
          <Text style={[styles.quantityButtonText, { color: theme.text.primary }]}>+1</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.primary }]}
          onPress={() => addToCart(item)}
        >
          <MaterialIcons name="add-shopping-cart" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>


          {/* Search Bar */}
          <View style={[styles.searchBar, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <MaterialIcons name="search" size={20} color={theme.text.secondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.text.primary }]}
              placeholder="Search products..."
              placeholderTextColor={theme.text.hint}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
              </TouchableOpacity>
            ) : null}
          </View>

          {/* Categories */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
            contentContainerStyle={styles.categoriesContent}
          >
            {categories.map(category => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  selectedCategory === category && { backgroundColor: theme.primary },
                  { borderColor: theme.border }
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <Text
                  style={[
                    styles.categoryButtonText,
                    { color: selectedCategory === category ? 'white' : theme.text.primary }
                  ]}
                >
                  {getCategoryName(category)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Products Grid */}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.primary} />
              <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading products...</Text>
            </View>
          ) : sortedProducts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="inventory" size={48} color={theme.text.disabled} />
              <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
                {searchQuery ? 'No products found matching your search' : 'No products available'}
              </Text>
            </View>
          ) : (
            <FlatList
              data={sortedProducts}
              renderItem={renderProductItem}
              keyExtractor={item => item.id}
              numColumns={2}
              contentContainerStyle={styles.productsList}
            />
          )}

          {/* Cart Button */}
          <View style={styles.cartButtonContainer}>
            <TouchableOpacity
              style={[styles.cartButton, { backgroundColor: theme.primary }]}
              onPress={() => navigation.navigate('Cart')}
            >
              <MaterialIcons name="shopping-cart" size={24} color="white" />
              {getCartCount() > 0 && (
                <View style={[styles.cartBadge, { backgroundColor: theme.error }]}>
                  <Text style={styles.cartBadgeText}>{getCartCount()}</Text>
                </View>
              )}
              <Text style={styles.cartButtonText}>View Cart</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 12,
  },

  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    height: 44,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  categoriesContainer: {
    marginBottom: 12,
  },
  categoriesContent: {
    paddingBottom: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  productsList: {
    paddingBottom: 80, // Extra padding for the cart button
  },
  productCard: {
    flex: 1,
    margin: 6,
    padding: 10,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  productImageContainer: {
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 8,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  productImagePlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  popularBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    zIndex: 1,
  },
  popularBadgeText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  productActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  quantityButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButtonContainer: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  cartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  cartBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  cartButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
});

import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
  Animated
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function ViewBillsScreen() {
  const [bills, setBills] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBill, setSelectedBill] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  useFocusEffect(
    React.useCallback(() => {
      loadBills();

      // Start fade-in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();

      return () => {};
    }, [])
  );

  const loadBills = async () => {
    try {
      setLoading(true);
      const data = await AsyncStorage.getItem('bills');
      const billsData = data ? JSON.parse(data) : [];
      // Sort bills by date (newest first)
      billsData.sort((a, b) => new Date(b.date) - new Date(a.date));
      setBills(billsData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load bills');
    } finally {
      setLoading(false);
    }
  };

  const viewBillDetails = (bill) => {
    setSelectedBill(bill);
    setModalVisible(true);
  };

  const deleteBill = async (id) => {
    try {
      const updatedBills = bills.filter(bill => bill.id !== id);
      await AsyncStorage.setItem('bills', JSON.stringify(updatedBills));
      setBills(updatedBills);
      setModalVisible(false);
      Alert.alert('Success', 'Bill deleted successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to delete bill');
    }
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      return dateString;
    }
  };

  const renderBillItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.billCard, { backgroundColor: theme.surface }]}
      onPress={() => viewBillDetails(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.billHeader, { backgroundColor: theme.card.background }]}>
        <Text style={[styles.billDate, { color: theme.text.secondary }]}>{formatDate(item.date)}</Text>
        <Text style={[styles.billAmount, { color: theme.success }]}>₹{parseFloat(item.amount).toFixed(2)}</Text>
      </View>

      <View style={[styles.billImageContainer, { backgroundColor: theme.card.background }]}>
        <Image source={{ uri: item.uri }} style={styles.billThumbnail} />
      </View>

      <View style={styles.billFooter}>
        <Text style={[styles.billItemsLabel, { color: theme.text.secondary }]}>Items:</Text>
        <Text style={[styles.billItems, { color: theme.text.secondary }]}>{item.items.join(', ')}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <Text style={[styles.title, { color: theme.text.primary }]}>Bills & Receipts</Text>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading bills...</Text>
          </View>
        ) : bills.length === 0 ? (
          <View style={[styles.emptyContainer, { backgroundColor: theme.surface }]}>
            <MaterialIcons name="receipt-long" size={64} color={theme.text.disabled} />
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No bills found</Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.primary }]}
              onPress={() => navigation.navigate('Capture Bill')}
            >
              <Text style={styles.addButtonText}>Capture Your First Bill</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={bills}
            keyExtractor={(item) => item.id}
            renderItem={renderBillItem}
            contentContainerStyle={styles.listContainer}
          />
        )}

        {/* Bill Detail Modal */}
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
              {selectedBill && (
                <>
                  <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                    <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Bill Details</Text>
                    <TouchableOpacity onPress={() => setModalVisible(false)}>
                      <MaterialIcons name="close" size={24} color={theme.text.primary} />
                    </TouchableOpacity>
                  </View>

                  <ScrollView style={styles.modalBody}>
                    <Image
                      source={{ uri: selectedBill.uri }}
                      style={styles.fullImage}
                      resizeMode="contain"
                    />

                    <View style={styles.detailRow}>
                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Date:</Text>
                      <Text style={[styles.detailValue, { color: theme.text.primary }]}>{formatDate(selectedBill.date)}</Text>
                    </View>

                    <View style={styles.detailRow}>
                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Amount:</Text>
                      <Text style={[styles.detailValue, { color: theme.text.primary }]}>₹{parseFloat(selectedBill.amount).toFixed(2)}</Text>
                    </View>

                    <View style={styles.detailRow}>
                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Items:</Text>
                      <View style={styles.itemsContainer}>
                        {selectedBill.items.map((item, index) => (
                          <View key={index} style={[styles.itemTag, { backgroundColor: theme.card.statsBlue }]}>
                            <Text style={[styles.itemTagText, { color: theme.isDarkMode ? '#fff' : '#1565c0' }]}>{item}</Text>
                          </View>
                        ))}
                      </View>
                    </View>

                    {selectedBill.notes ? (
                      <View style={styles.detailRow}>
                        <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Notes:</Text>
                        <Text style={[styles.detailValue, { color: theme.text.primary }]}>{selectedBill.notes}</Text>
                      </View>
                    ) : null}
                  </ScrollView>

                  <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                    <TouchableOpacity
                      style={[styles.deleteButton, { backgroundColor: theme.error }]}
                      onPress={() => {
                        Alert.alert(
                          'Delete Bill',
                          'Are you sure you want to delete this bill?',
                          [
                            { text: 'Cancel', style: 'cancel' },
                            { text: 'Delete', onPress: () => deleteBill(selectedBill.id), style: 'destructive' }
                          ]
                        );
                      }}
                    >
                      <MaterialIcons name="delete" size={18} color="white" />
                      <Text style={styles.deleteButtonText}>Delete Bill</Text>
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </View>
          </View>
        </Modal>

        {/* Floating Action Button */}
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: theme.primary }]}
          onPress={() => navigation.navigate('Capture Bill')}
        >
          <MaterialIcons name="add" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 80,
  },
  billCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
  },
  billHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#f0f0f0',
  },
  billDate: {
    fontSize: 16,
    fontWeight: '500',
    color: '#555',
  },
  billAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  billImageContainer: {
    height: 150,
    backgroundColor: '#eee',
  },
  billThumbnail: {
    width: '100%',
    height: '100%',
  },
  billFooter: {
    padding: 12,
  },
  billItemsLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 4,
  },
  billItems: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#4CAF50',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    padding: 16,
    maxHeight: 500,
  },
  fullImage: {
    width: '100%',
    height: 300,
    marginBottom: 16,
    borderRadius: 8,
  },
  detailRow: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#555',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
  },
  itemsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  itemTag: {
    backgroundColor: '#e3f2fd',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  itemTagText: {
    fontSize: 14,
    color: '#1565c0',
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  deleteButton: {
    backgroundColor: '#f44336',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 4,
  },
  deleteButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

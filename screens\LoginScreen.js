import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { AppContext } from '../AppContext';

export default function LoginScreen() {
  const { login, theme } = useContext(AppContext);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setLoading(true);
    const result = await login(email.trim(), password.trim());
    setLoading(false);

    if (!result.success) {
      Alert.alert('Login Failed', result.error || 'Invalid credentials');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={[styles.container, { backgroundColor: theme.background }]}
    >
      <View style={styles.innerContainer}>
        <Text style={[styles.title, { color: theme.text.primary }]}>Bakery Tracker</Text>

        <TextInput
          style={[styles.input, { backgroundColor: theme.input.background, color: theme.text.primary }]}
          placeholder="Email"
          placeholderTextColor={theme.text.hint}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <TextInput
          style={[styles.input, { backgroundColor: theme.input.background, color: theme.text.primary }]}
          placeholder="Password"
          placeholderTextColor={theme.text.hint}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />

        <TouchableOpacity
          onPress={handleLogin}
          disabled={loading}
          style={[styles.button, { backgroundColor: theme.primary }]}
        >
          <Text style={styles.buttonText}>{loading ? 'Logging in...' : 'Login'}</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  innerContainer: {
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBlockEnd: 32,
    textAlign: 'center',
  },
  input: {
    blockSize: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    insetBlockEnd: 16,
    fontSize: 16,
  },
  button: {
    blockSize: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    insetBlockStart: 12,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

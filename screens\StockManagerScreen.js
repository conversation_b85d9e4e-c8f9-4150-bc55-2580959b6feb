import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  RefreshControl,
  ScrollView
} from 'react-native';
import * as Print from 'expo-print';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function StockManagerScreen() {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const { theme } = useContext(AppContext);

  // Function to get low stock alerts
  const getLowStockAlerts = (recipes) => {
    const alerts = [];
    recipes.forEach(product => {
      product.ingredients.forEach(ing => {
        if (typeof ing.stock === 'number' && typeof ing.min === 'number' && ing.stock < ing.min) {
          alerts.push(`${product.name} - ${ing.name} is low: ${ing.stock}${ing.unit || ''}`);
        }
      });
    });
    return alerts;
  };

  // Calculate total ingredient cost
  const calculateTotalCost = (ingredient) => {
    const price = parseFloat(ingredient.price) || 0;
    const stock = parseFloat(ingredient.stock) || 0;
    return price * stock;
  };

  // Calculate total inventory value
  const calculateInventoryValue = (products) => {
    let total = 0;
    products.forEach(product => {
      product.ingredients.forEach(ing => {
        total += calculateTotalCost(ing);
      });
    });
    return total;
  };

  // Export stock to PDF
  const handleExport = async () => {
    try {
      setIsExporting(true);
      let content = `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #2c3e50; text-align: center; }
            h2 { color: #3498db; margin-top: 20px; }
            .product { background-color: #f8f9fa; padding: 10px; margin-bottom: 15px; border-radius: 5px; }
            .ingredient { margin-left: 20px; margin-bottom: 5px; }
            .low-stock { color: #e74c3c; font-weight: bold; }
            .date { text-align: right; color: #7f8c8d; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <h1>Stock Report</h1>
          <p class="date">Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
      `;

      // Add low stock alerts section if any
      const alerts = getLowStockAlerts(products);
      if (alerts.length > 0) {
        content += '<h2>⚠️ Low Stock Alerts</h2><ul>';
        alerts.forEach(alert => {
          content += `<li class="low-stock">${alert}</li>`;
        });
        content += '</ul>';
      }

      // Add product details
      content += '<h2>📦 Inventory Details</h2>';
      products.forEach(p => {
        content += `<div class="product"><h3>${p.name}</h3><ul>`;
        p.ingredients.forEach(i => {
          const isLow = (i.stock < i.min) ? 'low-stock' : '';
          const price = parseFloat(i.price) || 0;
          const totalCost = price * (i.stock || 0);
          content += `<li class="ingredient ${isLow}">${i.name}: ${i.stock || 0} ${i.unit || ''} (Min: ${i.min || 0}) - Price: ₹${price.toFixed(2)}/${i.unit || 'unit'} - Total: ₹${totalCost.toFixed(2)}</li>`;
        });
        content += '</ul></div>';
      });

      content += '</body></html>';

      await Print.printAsync({ html: content });
      Alert.alert('Success', 'Stock report generated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to generate stock report');
    } finally {
      setIsExporting(false);
    }
  };

  // Get navigation for the empty state button
  const navigation = useNavigation();

  useEffect(() => {
    loadRecipes();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadRecipes = async () => {
    try {
      setIsLoading(true);
      const keys = await AsyncStorage.getAllKeys();
      const recipes = keys.filter(k => k.startsWith('recipe_'));
      const recipeData = [];
      for (let key of recipes) {
        const raw = await AsyncStorage.getItem(key);
        const parsed = JSON.parse(raw);
        recipeData.push({ name: key.replace('recipe_', ''), ...parsed });
      }
      setProducts(recipeData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load recipes');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadRecipes();
  };

  const updateStock = async (recipeName, ingredientName, field, value) => {
    try {
      setIsLoading(true);
      const key = `recipe_${recipeName}`;
      const raw = await AsyncStorage.getItem(key);
      const recipe = JSON.parse(raw);
      const target = recipe.ingredients.find(i => i.name === ingredientName);

      if (target) {
        // Make sure the value is a valid number
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          Alert.alert('Error', 'Please enter a valid number');
          return;
        }

        target[field] = numValue;
        await AsyncStorage.setItem(key, JSON.stringify(recipe));

        // Success animation
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 0.5,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          })
        ]).start();
      }
      loadRecipes();
    } catch (error) {
      Alert.alert('Error', 'Failed to update stock');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <ScrollView
          contentContainerStyle={{ padding: 16 }}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={['#4CAF50']}
            />
          }
        >
        <View style={styles.headerContainer}>
          <Text style={[styles.title, { color: theme.text.primary }]}>Stock Manager</Text>
          <TouchableOpacity
            style={[styles.manageButton, { backgroundColor: theme.secondary }]}
            onPress={() => navigation.navigate('Ingredient Manager')}
          >
            <Text style={styles.manageButtonText}>Manage Ingredients</Text>
          </TouchableOpacity>
        </View>

        {/* Low Stock Alerts */}
        {getLowStockAlerts(products).length > 0 && (
          <View style={[styles.alertBox, { backgroundColor: theme.card.statsYellow, borderColor: theme.border }]}>
            <Text style={styles.alertTitle}>⚠️ Low Stock Alerts</Text>
            {getLowStockAlerts(products).map((msg, i) => (
              <Text key={i} style={styles.alertText}>{msg}</Text>
            ))}
          </View>
        )}

        {/* Export Button */}
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: theme.primary }, isExporting && { backgroundColor: theme.text.disabled }]}
          onPress={handleExport}
          disabled={isExporting}
        >
          {isExporting ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <View style={styles.exportButtonContent}>
              <MaterialIcons name="picture-as-pdf" size={18} color="white" />
              <Text style={styles.exportButtonText}>Export Stock to PDF</Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Inventory Value */}
        <View style={[styles.valueBox, { backgroundColor: theme.card.statsGreen }]}>
          <Text style={[styles.valueTitle, { color: theme.isDarkMode ? '#fff' : '#2e7d32' }]}>💰 Total Inventory Value</Text>
          <Text style={[styles.valueAmount, { color: theme.isDarkMode ? '#fff' : '#2e7d32' }]}>₹{calculateInventoryValue(products).toFixed(2)}</Text>
        </View>

        {/* Stock Summary */}
        <View style={[styles.summaryBox, { backgroundColor: theme.card.statsBlue }]}>
          <Text style={[styles.summaryTitle, { color: theme.isDarkMode ? '#fff' : '#1565c0' }]}>📦 Total Stock Summary</Text>
          {products.length === 0 ? (
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No products found</Text>
          ) : (
            products.map((product) => (
              <View key={product.name} style={[styles.productSummary, { backgroundColor: theme.surface }]}>
                <Text style={[styles.productSummaryTitle, { color: theme.text.primary }]}>{product.name}</Text>
                {product.ingredients.map((ing, i) => (
                  <View key={i} style={styles.ingredientSummaryRow}>
                    <Text style={[styles.ingredientSummary, { color: theme.text.secondary }, (ing.stock < ing.min) && { color: theme.status.lowStock }]}>
                      • {ing.name}: {ing.stock || 0} {ing.unit || 'units'}
                    </Text>
                    <Text style={[styles.ingredientPrice, { color: theme.success }]}>
                      ₹{parseFloat(ing.price || 0).toFixed(2)}/{ing.unit || 'unit'}
                    </Text>
                  </View>
                ))}
              </View>
            ))
          )}
        </View>

        {/* Stock Manager */}
        <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Update Stock Levels</Text>

        {isLoading && !isRefreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading stock data...</Text>
          </View>
        ) : products.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No products found</Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.primary }]}
              onPress={() => navigation.navigate('Add Recipe')}
            >
              <Text style={styles.addButtonText}>Add Your First Recipe</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.stockLevelsContainer}>
            {products.map((item) => (
              <View key={item.name} style={[styles.card, { backgroundColor: theme.surface }]}>
                <Text style={[styles.productTitle, { color: theme.text.primary }]}>{item.name}</Text>
                {item.ingredients.map((ing, i) => (
                  <View key={i} style={[styles.row, { backgroundColor: theme.card.background }]}>
                    <Text style={[styles.ingredientName, { color: theme.text.primary }]}>{ing.name}</Text>
                    <View style={styles.stockControls}>
                      <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Current</Text>
                      <TextInput
                        style={[styles.input, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }, (ing.stock < ing.min) && { borderColor: theme.status.lowStock, backgroundColor: theme.card.statsRed }]}
                        placeholderTextColor={theme.text.hint}
                        keyboardType="numeric"
                        value={ing.stock?.toString() || '0'}
                        onEndEditing={(e) => updateStock(item.name, ing.name, 'stock', e.nativeEvent.text)}
                      />
                    </View>
                    <View style={styles.stockControls}>
                      <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Min</Text>
                      <TextInput
                        style={[styles.input, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
                        placeholderTextColor={theme.text.hint}
                        keyboardType="numeric"
                        value={ing.min?.toString() || '5'}
                        onEndEditing={(e) => updateStock(item.name, ing.name, 'min', e.nativeEvent.text)}
                      />
                    </View>
                    <View style={styles.stockControls}>
                      <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Price (₹)</Text>
                      <TextInput
                        style={[styles.input, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
                        placeholderTextColor={theme.text.hint}
                        keyboardType="numeric"
                        value={ing.price?.toString() || '0'}
                        onEndEditing={(e) => updateStock(item.name, ing.name, 'price', e.nativeEvent.text)}
                        placeholder="0.00"
                      />
                    </View>
                  </View>
                ))}
              </View>
            ))}
          </View>
        )}
        </ScrollView>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  valueBox: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#e8f5e9',
    marginBottom: 16,
    elevation: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  valueTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2e7d32',
  },
  valueAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2e7d32',
  },
  container: {
    flex: 1,
  },
  stockLevelsContainer: {
    paddingBottom: 100,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  manageButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  manageButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 12,
    color: '#555'
  },
  summaryBox: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#e3f2fd',
    marginBottom: 16,
    elevation: 1,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#1565c0',
  },
  productSummary: {
    marginBottom: 10,
    backgroundColor: '#f5f5f5',
    padding: 10,
    borderRadius: 6,
  },
  productSummaryTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
    color: '#333',
  },
  ingredientSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 5,
  },
  ingredientSummary: {
    fontSize: 14,
    marginLeft: 10,
    color: '#555',
    flex: 1,
  },
  ingredientPrice: {
    fontSize: 14,
    color: '#2e7d32',
    fontWeight: '500',
  },
  card: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 1,
  },
  productTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 6,
  },
  ingredientName: {
    flex: 1,
    fontSize: 15,
    color: '#333',
  },
  stockControls: {
    alignItems: 'center',
    marginLeft: 8,
  },
  inputLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  input: {
    width: 75,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 4,
    backgroundColor: '#fff',
    textAlign: 'center',
    fontSize: 16,
  },
  lowStockInput: {
    borderColor: '#e74c3c',
    backgroundColor: '#ffebee',
  },
  lowStockText: {
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  alertBox: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffeeba',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  alertTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#856404',
    marginBottom: 8,
  },
  alertText: {
    color: '#856404',
    fontSize: 14,
    marginBottom: 4,
  },
  exportButton: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
    elevation: 2,
  },
  exportButtonDisabled: {
    backgroundColor: '#a5d6a7',
  },
  exportButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  exportButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

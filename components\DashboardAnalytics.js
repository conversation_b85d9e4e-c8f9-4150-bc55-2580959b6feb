import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  Alert,
  Modal,
  Platform
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppContext } from '../AppContext';
import AnalyticsExportService from '../utils/AnalyticsExportService';

// Using custom date picker instead of @react-native-community/datetimepicker

const FILTER_OPTIONS = [
  { id: 'day', label: 'Today', days: 1 },
  { id: 'week', label: 'Last Week', days: 7 },
  { id: 'month', label: 'Last Month', days: 30 },
  { id: '6month', label: 'Last 6 Months', days: 180 },
  { id: 'year', label: 'Last Year', days: 365 },
  { id: 'custom', label: 'Custom Date', days: 1 }
];

export default function DashboardAnalytics() {
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState(null);
  const [analyticsHistory, setAnalyticsHistory] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState('day');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  const { theme } = useContext(AppContext);
  const screenWidth = Dimensions.get('window').width - 40;

  useEffect(() => {
    loadAnalytics();
    checkDailyReset();

    // Set up a timer to check for daily reset every minute
    const intervalId = setInterval(() => {
      checkDailyReset();
    }, 60000); // Check every minute

    return () => clearInterval(intervalId); // Clean up on unmount
  }, []);



  const checkDailyReset = async () => {
    try {
      const lastResetDate = await AsyncStorage.getItem('analytics_last_reset');
      const today = new Date().toISOString().split('T')[0];

      if (!lastResetDate || lastResetDate !== today) {
        console.log('Performing daily analytics reset...');

        // Save current analytics to history before resetting
        const currentAnalytics = await AsyncStorage.getItem('current_analytics');
        if (currentAnalytics) {
          const parsedAnalytics = JSON.parse(currentAnalytics);

          // Add date to analytics
          parsedAnalytics.date = new Date().toISOString();

          // Save to history
          const history = await AsyncStorage.getItem('analytics_history');
          const parsedHistory = history ? JSON.parse(history) : [];
          parsedHistory.push(parsedAnalytics);
          await AsyncStorage.setItem('analytics_history', JSON.stringify(parsedHistory));

          // Reset current analytics
          const resetAnalytics = {
            totalProducts: 0,
            totalRevenue: 0,
            totalCost: 0,
            totalProfit: 0,
            date: new Date().toISOString()
          };
          await AsyncStorage.setItem('current_analytics', JSON.stringify(resetAnalytics));

          // Also reset production data to ensure dashboard metrics are updated
          await AsyncStorage.setItem('production_data', JSON.stringify([]));

          // Update last reset date
          await AsyncStorage.setItem('analytics_last_reset', today);

          // Update the UI
          setAnalytics(resetAnalytics);

          // Force refresh of parent component if available
          if (window.refreshDashboard && typeof window.refreshDashboard === 'function') {
            window.refreshDashboard();
          }

          console.log('Daily reset completed successfully');
        }
      }
    } catch (error) {
      console.error('Error checking daily reset:', error);
    }
  };

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      // Load current analytics
      const currentAnalyticsData = await AsyncStorage.getItem('current_analytics');
      let currentAnalytics;

      if (currentAnalyticsData) {
        currentAnalytics = JSON.parse(currentAnalyticsData);
      } else {
        // Initialize analytics if not exists
        currentAnalytics = {
          totalProducts: 0,
          totalRevenue: 0,
          totalCost: 0,
          totalProfit: 0,
          date: new Date().toISOString()
        };
        await AsyncStorage.setItem('current_analytics', JSON.stringify(currentAnalytics));
      }

      setAnalytics(currentAnalytics);

      // Load analytics history
      const historyData = await AsyncStorage.getItem('analytics_history');
      if (historyData) {
        const parsedHistory = JSON.parse(historyData);
        // Sort by date (newest first)
        parsedHistory.sort((a, b) => new Date(b.date) - new Date(a.date));
        setAnalyticsHistory(parsedHistory);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading analytics:', error);
      setLoading(false);
    }
  };



  const formatCurrency = (amount) => {
    return `₹${parseFloat(amount).toFixed(2)}`;
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
    setSelectedFilter('custom');
    setShowDatePicker(false);
  };

  const resetAnalytics = async () => {
    try {
      // Reset current analytics
      const resetAnalytics = {
        totalProducts: 0,
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        date: new Date().toISOString()
      };

      // Save to AsyncStorage
      await AsyncStorage.setItem('current_analytics', JSON.stringify(resetAnalytics));

      // Also reset production data to ensure dashboard metrics are updated
      await AsyncStorage.setItem('production_data', JSON.stringify([]));

      // Reload analytics
      setAnalytics(resetAnalytics);
      setShowResetConfirm(false);

      // Force refresh of parent component
      if (window.refreshDashboard && typeof window.refreshDashboard === 'function') {
        window.refreshDashboard();
      }

      Alert.alert(
        'Success',
        'Analytics data has been reset successfully. Please reload the dashboard to see the changes.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Try to force a refresh of the app
              if (Platform.OS === 'web') {
                window.location.reload();
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error resetting analytics:', error);
      Alert.alert('Error', 'Failed to reset analytics data. Please try again.');
    }
  };

  const renderMetricCard = (title, value, icon, color, percentChange = null) => (
    <View style={[styles.metricCard, { backgroundColor: theme.surface }]}>
      <View style={styles.metricHeader}>
        <MaterialIcons name={icon} size={24} color={color} />
        <Text style={[styles.metricTitle, { color: theme.text.secondary }]}>{title}</Text>
      </View>
      <Text style={[styles.metricValue, { color: theme.text.primary }]}>{value}</Text>
      {percentChange !== null && (
        <View style={styles.percentChangeContainer}>
          <MaterialIcons
            name={percentChange >= 0 ? 'arrow-upward' : 'arrow-downward'}
            size={16}
            color={percentChange >= 0 ? theme.success : theme.error}
          />
          <Text
            style={[
              styles.percentChangeText,
              { color: percentChange >= 0 ? theme.success : theme.error }
            ]}
          >
            {Math.abs(percentChange).toFixed(1)}% from previous
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading analytics...</Text>
        </View>
      ) : (
        <>
          {/* Filter Options */}
          <View style={styles.filterHeader}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.filterContainer}
              contentContainerStyle={styles.filterContent}
            >
              {FILTER_OPTIONS.map(option => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterButton,
                    selectedFilter === option.id && { backgroundColor: theme.primary },
                    { borderColor: theme.border }
                  ]}
                  onPress={() => {
                    setSelectedFilter(option.id);
                    if (option.id === 'custom') {
                      setShowDatePicker(true);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.filterButtonText,
                      { color: selectedFilter === option.id ? 'white' : theme.text.primary }
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={[styles.resetButton, { backgroundColor: theme.error }]}
              onPress={() => setShowResetConfirm(true)}
            >
              <MaterialIcons name="refresh" size={16} color="white" />
              <Text style={styles.resetButtonText}>Reset</Text>
            </TouchableOpacity>
          </View>

          {/* Date display for custom filter */}
          {selectedFilter === 'custom' && (
            <TouchableOpacity
              style={[styles.dateDisplay, { borderColor: theme.border }]}
              onPress={() => setShowDatePicker(true)}
            >
              <MaterialIcons name="calendar-today" size={16} color={theme.text.secondary} />
              <Text style={[styles.dateDisplayText, { color: theme.text.primary }]}>
                {selectedDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          )}

          {/* Custom Date Picker Modal */}
          {showDatePicker && (
            <Modal
              transparent={true}
              visible={showDatePicker}
              animationType="slide"
              onRequestClose={() => setShowDatePicker(false)}
            >
              <View style={styles.datePickerModal}>
                <View style={[styles.datePickerContainer, { backgroundColor: theme.surface }]}>
                  <View style={styles.datePickerHeader}>
                    <Text style={[styles.datePickerTitle, { color: theme.text.primary }]}>Select Date</Text>
                    <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                      <MaterialIcons name="close" size={24} color={theme.text.primary} />
                    </TouchableOpacity>
                  </View>

                  {/* Custom Date Picker */}
                  <View style={styles.customDatePicker}>
                    {/* Year Picker */}
                    <View style={styles.datePickerColumn}>
                      <TouchableOpacity
                        style={styles.dateArrow}
                        onPress={() => {
                          const newDate = new Date(selectedDate);
                          newDate.setFullYear(newDate.getFullYear() - 1);
                          setSelectedDate(newDate);
                        }}
                      >
                        <MaterialIcons name="arrow-drop-up" size={24} color={theme.text.secondary} />
                      </TouchableOpacity>
                      <Text style={[styles.dateValue, { color: theme.text.primary }]}>
                        {selectedDate.getFullYear()}
                      </Text>
                      <TouchableOpacity
                        style={styles.dateArrow}
                        onPress={() => {
                          const newDate = new Date(selectedDate);
                          newDate.setFullYear(newDate.getFullYear() + 1);
                          setSelectedDate(newDate);
                        }}
                      >
                        <MaterialIcons name="arrow-drop-down" size={24} color={theme.text.secondary} />
                      </TouchableOpacity>
                    </View>

                    {/* Month Picker */}
                    <View style={styles.datePickerColumn}>
                      <TouchableOpacity
                        style={styles.dateArrow}
                        onPress={() => {
                          const newDate = new Date(selectedDate);
                          newDate.setMonth(newDate.getMonth() - 1);
                          setSelectedDate(newDate);
                        }}
                      >
                        <MaterialIcons name="arrow-drop-up" size={24} color={theme.text.secondary} />
                      </TouchableOpacity>
                      <Text style={[styles.dateValue, { color: theme.text.primary }]}>
                        {selectedDate.toLocaleString('default', { month: 'short' })}
                      </Text>
                      <TouchableOpacity
                        style={styles.dateArrow}
                        onPress={() => {
                          const newDate = new Date(selectedDate);
                          newDate.setMonth(newDate.getMonth() + 1);
                          setSelectedDate(newDate);
                        }}
                      >
                        <MaterialIcons name="arrow-drop-down" size={24} color={theme.text.secondary} />
                      </TouchableOpacity>
                    </View>

                    {/* Day Picker */}
                    <View style={styles.datePickerColumn}>
                      <TouchableOpacity
                        style={styles.dateArrow}
                        onPress={() => {
                          const newDate = new Date(selectedDate);
                          newDate.setDate(newDate.getDate() - 1);
                          setSelectedDate(newDate);
                        }}
                      >
                        <MaterialIcons name="arrow-drop-up" size={24} color={theme.text.secondary} />
                      </TouchableOpacity>
                      <Text style={[styles.dateValue, { color: theme.text.primary }]}>
                        {selectedDate.getDate()}
                      </Text>
                      <TouchableOpacity
                        style={styles.dateArrow}
                        onPress={() => {
                          const newDate = new Date(selectedDate);
                          newDate.setDate(newDate.getDate() + 1);
                          setSelectedDate(newDate);
                        }}
                      >
                        <MaterialIcons name="arrow-drop-down" size={24} color={theme.text.secondary} />
                      </TouchableOpacity>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={[styles.datePickerButton, { backgroundColor: theme.primary }]}
                    onPress={() => handleDateChange(selectedDate)}
                  >
                    <Text style={styles.datePickerButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Modal>
          )}

          {/* Metrics Cards */}
          <ScrollView style={styles.metricsContainer}>
            <View style={styles.metricsGrid}>
              {renderMetricCard(
                'Total Products',
                analytics?.totalProducts || 0,
                'inventory',
                theme.primary
              )}

              {renderMetricCard(
                'Revenue',
                formatCurrency(analytics?.totalRevenue || 0),
                'payments',
                theme.success
              )}

              {renderMetricCard(
                'Cost',
                formatCurrency(analytics?.totalCost || 0),
                'account-balance-wallet',
                theme.error
              )}

              {renderMetricCard(
                'Profit',
                formatCurrency(analytics?.totalProfit || 0),
                'trending-up',
                theme.accent
              )}
            </View>

            {/* Performance Summary */}
            <View style={[styles.chartContainer, { backgroundColor: theme.surface }]}>
              <Text style={[styles.chartTitle, { color: theme.text.primary }]}>
                {selectedFilter === 'day' ? 'Today\'s Performance' : 'Performance Summary'}
              </Text>
              <View style={styles.performanceSummary}>
                <View style={styles.performanceItem}>
                  <MaterialIcons name="trending-up" size={24} color={theme.success} />
                  <Text style={[styles.performanceLabel, { color: theme.text.secondary }]}>Revenue</Text>
                  <Text style={[styles.performanceValue, { color: theme.success }]}>
                    {formatCurrency(analytics?.totalRevenue || 0)}
                  </Text>
                </View>
                <View style={styles.performanceItem}>
                  <MaterialIcons name="trending-down" size={24} color={theme.error} />
                  <Text style={[styles.performanceLabel, { color: theme.text.secondary }]}>Cost</Text>
                  <Text style={[styles.performanceValue, { color: theme.error }]}>
                    {formatCurrency(analytics?.totalCost || 0)}
                  </Text>
                </View>
                <View style={styles.performanceItem}>
                  <MaterialIcons name="account-balance" size={24} color={theme.primary} />
                  <Text style={[styles.performanceLabel, { color: theme.text.secondary }]}>Profit</Text>
                  <Text style={[styles.performanceValue, { color: theme.primary }]}>
                    {formatCurrency(analytics?.totalProfit || 0)}
                  </Text>
                </View>
              </View>
              <View style={styles.filterInfo}>
                <MaterialIcons name="date-range" size={16} color={theme.text.secondary} />
                <Text style={[styles.filterInfoText, { color: theme.text.secondary }]}>
                  Showing data for: {FILTER_OPTIONS.find(option => option.id === selectedFilter)?.label}
                </Text>
              </View>
            </View>

            {/* Reset Info */}
            <View style={[styles.resetInfoContainer, { backgroundColor: theme.card.background }]}>
              <MaterialIcons name="info-outline" size={20} color={theme.text.secondary} />
              <Text style={[styles.resetInfoText, { color: theme.text.secondary }]}>
                Analytics automatically reset at midnight every day. Historical data is preserved and can be viewed in the History section. You can also manually reset the data using the Reset button.
              </Text>
            </View>

            {/* Reset Confirmation Modal */}
            <Modal
              transparent={true}
              visible={showResetConfirm}
              animationType="fade"
              onRequestClose={() => setShowResetConfirm(false)}
            >
              <View style={styles.modalOverlay}>
                <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                  <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Reset Analytics</Text>
                  <Text style={[styles.modalMessage, { color: theme.text.secondary }]}>
                    Are you sure you want to reset all analytics data? This will set all current metrics to zero.
                  </Text>
                  <View style={styles.modalButtons}>
                    <TouchableOpacity
                      style={[styles.modalButton, styles.cancelButton, { borderColor: theme.border }]}
                      onPress={() => setShowResetConfirm(false)}
                    >
                      <Text style={[styles.modalButtonText, { color: theme.text.primary }]}>Cancel</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.modalButton, styles.confirmButton, { backgroundColor: theme.error }]}
                      onPress={resetAnalytics}
                    >
                      <Text style={[styles.modalButtonText, { color: 'white' }]}>Reset</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
          </ScrollView>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterContent: {
    paddingHorizontal: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  metricsContainer: {
    flex: 1,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    width: '48%',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 14,
    marginLeft: 8,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  percentChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  percentChangeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  chartContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  performanceSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  performanceItem: {
    alignItems: 'center',
    flex: 1,
  },
  performanceLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  performanceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
  filterInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  filterInfoText: {
    fontSize: 12,
    marginLeft: 4,
  },
  resetInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  resetInfoText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  resetButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  dateDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  dateDisplayText: {
    fontSize: 14,
    marginLeft: 8,
  },
  datePickerModal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  datePickerContainer: {
    width: '80%',
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 16,
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  datePickerButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 16,
  },
  datePickerButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  customDatePicker: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingVertical: 20,
  },
  datePickerColumn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateArrow: {
    padding: 8,
  },
  dateValue: {
    fontSize: 22,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  modalMessage: {
    fontSize: 14,
    marginBottom: 20,
    lineHeight: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    marginRight: 8,
  },
  confirmButton: {
    marginLeft: 8,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

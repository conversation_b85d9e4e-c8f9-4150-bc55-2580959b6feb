import React, { useContext } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { AppContext } from '../AppContext';

// Import billing system screens
import BillingHomeScreen from './BillingHomeScreen';
import ProductSelectionScreen from './ProductSelectionScreen';
import CartScreen from './CartScreen';
import BillingHistoryScreen from './BillingHistoryScreen';
import ProductManagementScreen from './ProductManagementScreen';
import ShopProfileScreen from './ShopProfileScreen';

const Stack = createStackNavigator();

export default function BillingStack() {
  const { theme } = useContext(AppContext);

  return (
    <Stack.Navigator
      initialRouteName="BillingHome"
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="BillingHome"
        component={BillingHomeScreen}
        options={{ title: 'Billing System' }}
      />
      <Stack.Screen
        name="ProductSelection"
        component={ProductSelectionScreen}
        options={{ title: 'Select Products' }}
      />
      <Stack.Screen
        name="Cart"
        component={CartScreen}
        options={{ title: 'Shopping Cart' }}
      />
      <Stack.Screen
        name="BillingHistory"
        component={BillingHistoryScreen}
        options={{ title: 'Sales History' }}
      />
      <Stack.Screen
        name="ProductManagement"
        component={ProductManagementScreen}
        options={{ title: 'Manage Products' }}
      />
      <Stack.Screen
        name="ShopProfile"
        component={ShopProfileScreen}
        options={{ title: 'Shop Profile' }}
      />
    </Stack.Navigator>
  );
}

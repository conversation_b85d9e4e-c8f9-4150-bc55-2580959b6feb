import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
  Animated,
  Modal,
  FlatList,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';

export default function IngredientManagerScreen() {
  const [ingredients, setIngredients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentIngredient, setCurrentIngredient] = useState(null);
  const [name, setName] = useState('');
  const [price, setPrice] = useState('');
  const [unit, setUnit] = useState('');
  const [stock, setStock] = useState('');
  const [minStock, setMinStock] = useState('');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();

  useFocusEffect(
    React.useCallback(() => {
      loadIngredients();
      
      // Start fade-in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
      
      return () => {};
    }, [])
  );

  const loadIngredients = async () => {
    try {
      setLoading(true);
      
      // Load ingredients from AsyncStorage
      const ingredientsData = await AsyncStorage.getItem('ingredients');
      let parsedIngredients = ingredientsData ? JSON.parse(ingredientsData) : [];
      
      // Sort ingredients alphabetically
      parsedIngredients.sort((a, b) => a.name.localeCompare(b.name));
      
      setIngredients(parsedIngredients);
    } catch (error) {
      Alert.alert('Error', 'Failed to load ingredients');
    } finally {
      setLoading(false);
    }
  };

  const openAddModal = () => {
    setEditMode(false);
    setCurrentIngredient(null);
    setName('');
    setPrice('');
    setUnit('kg');
    setStock('0');
    setMinStock('5');
    setModalVisible(true);
  };

  const openEditModal = (ingredient) => {
    setEditMode(true);
    setCurrentIngredient(ingredient);
    setName(ingredient.name);
    setPrice(ingredient.price?.toString() || '');
    setUnit(ingredient.unit || 'kg');
    setStock(ingredient.stock?.toString() || '0');
    setMinStock(ingredient.min?.toString() || '5');
    setModalVisible(true);
  };

  const saveIngredient = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Ingredient name is required');
      return;
    }

    try {
      // Create new ingredient object
      const ingredient = {
        id: editMode && currentIngredient ? currentIngredient.id : Date.now().toString(),
        name: name.trim(),
        price: price ? parseFloat(price) : 0,
        unit: unit || 'kg',
        stock: stock ? parseFloat(stock) : 0,
        min: minStock ? parseFloat(minStock) : 5,
        createdAt: editMode && currentIngredient ? currentIngredient.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Get existing ingredients
      const ingredientsData = await AsyncStorage.getItem('ingredients');
      let parsedIngredients = ingredientsData ? JSON.parse(ingredientsData) : [];
      
      if (editMode && currentIngredient) {
        // Update existing ingredient
        const index = parsedIngredients.findIndex(item => item.id === currentIngredient.id);
        if (index !== -1) {
          parsedIngredients[index] = ingredient;
        }
      } else {
        // Check for duplicate names
        const exists = parsedIngredients.some(item => 
          item.name.toLowerCase() === ingredient.name.toLowerCase()
        );
        
        if (exists) {
          Alert.alert('Error', 'An ingredient with this name already exists');
          return;
        }
        
        // Add new ingredient
        parsedIngredients.push(ingredient);
      }
      
      // Save to AsyncStorage
      await AsyncStorage.setItem('ingredients', JSON.stringify(parsedIngredients));
      
      // Reload ingredients
      await loadIngredients();
      
      // Close modal
      setModalVisible(false);
      
      // Show success message
      Alert.alert(
        'Success', 
        editMode ? 'Ingredient updated successfully' : 'Ingredient added successfully'
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save ingredient');
    }
  };

  const deleteIngredient = async (ingredient) => {
    try {
      // Get existing ingredients
      const ingredientsData = await AsyncStorage.getItem('ingredients');
      let parsedIngredients = ingredientsData ? JSON.parse(ingredientsData) : [];
      
      // Filter out the ingredient to delete
      parsedIngredients = parsedIngredients.filter(item => item.id !== ingredient.id);
      
      // Save to AsyncStorage
      await AsyncStorage.setItem('ingredients', JSON.stringify(parsedIngredients));
      
      // Reload ingredients
      await loadIngredients();
      
      // Close modal if open
      setModalVisible(false);
      
      // Show success message
      Alert.alert('Success', 'Ingredient deleted successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to delete ingredient');
    }
  };

  const confirmDelete = (ingredient) => {
    Alert.alert(
      'Delete Ingredient',
      `Are you sure you want to delete ${ingredient.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          onPress: () => deleteIngredient(ingredient),
          style: 'destructive' 
        }
      ]
    );
  };

  const renderIngredientItem = ({ item }) => (
    <View style={styles.ingredientCard}>
      <View style={styles.ingredientHeader}>
        <Text style={styles.ingredientName}>{item.name}</Text>
        <View style={[
          styles.stockBadge, 
          (item.stock < item.min) ? styles.lowStockBadge : styles.goodStockBadge
        ]}>
          <Text style={styles.stockBadgeText}>
            {item.stock < item.min ? 'Low Stock' : 'In Stock'}
          </Text>
        </View>
      </View>
      
      <View style={styles.ingredientDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Price:</Text>
          <Text style={styles.detailValue}>₹{item.price?.toFixed(2) || '0.00'}/{item.unit || 'kg'}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Current Stock:</Text>
          <Text style={[
            styles.detailValue, 
            (item.stock < item.min) && styles.lowStockText
          ]}>
            {item.stock || '0'} {item.unit || 'kg'}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Min Stock:</Text>
          <Text style={styles.detailValue}>{item.min || '5'} {item.unit || 'kg'}</Text>
        </View>
      </View>
      
      <View style={styles.ingredientActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => openEditModal(item)}
        >
          <MaterialIcons name="edit" size={18} color="#2196F3" />
          <Text style={[styles.actionButtonText, { color: '#2196F3' }]}>Edit</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => confirmDelete(item)}
        >
          <MaterialIcons name="delete" size={18} color="#F44336" />
          <Text style={[styles.actionButtonText, { color: '#F44336' }]}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <Text style={styles.title}>Ingredient Manager</Text>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4CAF50" />
            <Text style={styles.loadingText}>Loading ingredients...</Text>
          </View>
        ) : ingredients.length === 0 ? (
          <View style={styles.emptyContainer}>
            <MaterialIcons name="shopping-basket" size={64} color="#ccc" />
            <Text style={styles.emptyText}>No ingredients found</Text>
            <Text style={styles.emptySubText}>
              Add ingredients to manage your inventory
            </Text>
            <TouchableOpacity 
              style={styles.addButton}
              onPress={openAddModal}
            >
              <Text style={styles.addButtonText}>Add Your First Ingredient</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={ingredients}
            keyExtractor={(item) => item.id}
            renderItem={renderIngredientItem}
            contentContainerStyle={styles.listContainer}
          />
        )}
        
        {/* Ingredient Form Modal */}
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setModalVisible(false)}
        >
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.modalContainer}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {editMode ? 'Edit Ingredient' : 'Add Ingredient'}
                </Text>
                <TouchableOpacity onPress={() => setModalVisible(false)}>
                  <MaterialIcons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.modalBody}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Name *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={name}
                    onChangeText={setName}
                    placeholder="Enter ingredient name"
                  />
                </View>
                
                <View style={styles.formRow}>
                  <View style={[styles.formGroup, { flex: 2, marginRight: 8 }]}>
                    <Text style={styles.formLabel}>Price (₹)</Text>
                    <TextInput
                      style={styles.formInput}
                      value={price}
                      onChangeText={setPrice}
                      placeholder="0.00"
                      keyboardType="numeric"
                    />
                  </View>
                  
                  <View style={[styles.formGroup, { flex: 1 }]}>
                    <Text style={styles.formLabel}>Unit</Text>
                    <TextInput
                      style={styles.formInput}
                      value={unit}
                      onChangeText={setUnit}
                      placeholder="kg"
                    />
                  </View>
                </View>
                
                <View style={styles.formRow}>
                  <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                    <Text style={styles.formLabel}>Current Stock</Text>
                    <TextInput
                      style={styles.formInput}
                      value={stock}
                      onChangeText={setStock}
                      placeholder="0"
                      keyboardType="numeric"
                    />
                  </View>
                  
                  <View style={[styles.formGroup, { flex: 1 }]}>
                    <Text style={styles.formLabel}>Min Stock</Text>
                    <TextInput
                      style={styles.formInput}
                      value={minStock}
                      onChangeText={setMinStock}
                      placeholder="5"
                      keyboardType="numeric"
                    />
                  </View>
                </View>
              </ScrollView>
              
              <View style={styles.modalFooter}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={() => setModalVisible(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.saveButton}
                  onPress={saveIngredient}
                >
                  <Text style={styles.saveButtonText}>
                    {editMode ? 'Update' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </KeyboardAvoidingView>
        </Modal>
        
        {/* Floating Action Button */}
        <TouchableOpacity 
          style={styles.fab}
          onPress={openAddModal}
        >
          <MaterialIcons name="add" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 80,
  },
  ingredientCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
  },
  ingredientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f0f0f0',
  },
  ingredientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  stockBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  goodStockBadge: {
    backgroundColor: '#e8f5e9',
  },
  lowStockBadge: {
    backgroundColor: '#ffebee',
  },
  stockBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  ingredientDetails: {
    padding: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  lowStockText: {
    color: '#e53935',
  },
  ingredientActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  actionButtonText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 24,
    textAlign: 'center',
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#4CAF50',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    justifyContent: 'flex-end',
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    fontWeight: '500',
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
    fontSize: 16,
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

import AsyncStorage from '@react-native-async-storage/async-storage';

// Default API URL (change this to your deployed server URL)
const DEFAULT_API_URL = 'https://bakery-tracker-server.onrender.com';

// Get the API URL from AsyncStorage
export const getAPIURL = async () => {
  try {
    const apiURL = await AsyncStorage.getItem('API_URL');
    return apiURL || DEFAULT_API_URL;
  } catch (error) {
    console.error('Error getting API URL:', error);
    return DEFAULT_API_URL;
  }
};

// Set the API URL in AsyncStorage
export const setAPIURL = async (url) => {
  try {
    await AsyncStorage.setItem('API_URL', url);
    return true;
  } catch (error) {
    console.error('Error setting API URL:', error);
    return false;
  }
};

// Function to make a request to the API
export const callAPI = async (method, endpoint, data = null) => {
  try {
    const apiURL = await getAPIURL();
    const url = `${apiURL}${endpoint}`;
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }
    
    console.log(`API Request (${method} ${endpoint}):`, options);
    
    const response = await fetch(url, options);
    const responseData = await response.json();
    
    console.log(`API Response (${method} ${endpoint}):`, responseData);
    
    return { success: response.ok, data: responseData };
  } catch (error) {
    console.error(`Error calling API (${method} ${endpoint}):`, error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to get all documents from a collection
export const getDocuments = async (collection, filter = {}) => {
  try {
    const filterString = encodeURIComponent(JSON.stringify(filter));
    const result = await callAPI('GET', `/api/${collection}?filter=${filterString}`);
    
    if (result.success && result.data) {
      return result.data;
    }
    
    return [];
  } catch (error) {
    console.error(`Error getting documents from ${collection}:`, error);
    return [];
  }
};

// Function to get a document by ID
export const getDocumentById = async (collection, id) => {
  try {
    const result = await callAPI('GET', `/api/${collection}/${id}`);
    
    if (result.success && result.data) {
      return result.data;
    }
    
    return null;
  } catch (error) {
    console.error(`Error getting document from ${collection}:`, error);
    return null;
  }
};

// Function to create a document
export const createDocument = async (collection, document) => {
  try {
    const result = await callAPI('POST', `/api/${collection}`, document);
    
    if (result.success && result.data) {
      return { success: true, data: result.data };
    }
    
    return { success: false, error: result.error || 'Failed to create document' };
  } catch (error) {
    console.error(`Error creating document in ${collection}:`, error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to update a document
export const updateDocument = async (collection, id, updates) => {
  try {
    const result = await callAPI('PUT', `/api/${collection}/${id}`, updates);
    
    if (result.success && result.data) {
      return { success: true, data: result.data };
    }
    
    return { success: false, error: result.error || 'Failed to update document' };
  } catch (error) {
    console.error(`Error updating document in ${collection}:`, error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to delete a document
export const deleteDocument = async (collection, id) => {
  try {
    const result = await callAPI('DELETE', `/api/${collection}/${id}`);
    
    if (result.success && result.data) {
      return { success: true, data: result.data };
    }
    
    return { success: false, error: result.error || 'Failed to delete document' };
  } catch (error) {
    console.error(`Error deleting document from ${collection}:`, error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to save a product
export const saveProduct = async (product) => {
  try {
    if (product._id) {
      // Update existing product
      return await updateDocument('products', product._id, product);
    } else {
      // Create new product
      return await createDocument('products', product);
    }
  } catch (error) {
    console.error('Error saving product:', error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to get all products
export const getProducts = async (filter = {}) => {
  try {
    return await getDocuments('products', filter);
  } catch (error) {
    console.error('Error getting products:', error);
    return [];
  }
};

// Function to delete a product
export const deleteProduct = async (id) => {
  try {
    return await deleteDocument('products', id);
  } catch (error) {
    console.error('Error deleting product:', error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to save an ingredient
export const saveIngredient = async (ingredient) => {
  try {
    if (ingredient._id) {
      // Update existing ingredient
      return await updateDocument('ingredients', ingredient._id, ingredient);
    } else {
      // Create new ingredient
      return await createDocument('ingredients', ingredient);
    }
  } catch (error) {
    console.error('Error saving ingredient:', error);
    return { success: false, error: error.message || 'Unknown error' };
  }
};

// Function to get all ingredients
export const getIngredients = async (filter = {}) => {
  try {
    return await getDocuments('ingredients', filter);
  } catch (error) {
    console.error('Error getting ingredients:', error);
    return [];
  }
};

// Export all functions
export default {
  getAPIURL,
  setAPIURL,
  callAPI,
  getDocuments,
  getDocumentById,
  createDocument,
  updateDocument,
  deleteDocument,
  saveProduct,
  getProducts,
  deleteProduct,
  saveIngredient,
  getIngredients
};

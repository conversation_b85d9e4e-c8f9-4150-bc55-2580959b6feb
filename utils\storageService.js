import AsyncStorage from '@react-native-async-storage/async-storage';

export const saveData = async (key, value) => {
  try {
    await AsyncStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error saving [${key}]:`, error);
  }
};

export const getData = async (key) => {
  try {
    const raw = await AsyncStorage.getItem(key);
    return raw ? JSON.parse(raw) : null;
  } catch (error) {
    console.error(`Error reading [${key}]:`, error);
    return null;
  }
};

export const appendToList = async (key, item) => {
  const list = (await getData(key)) || [];
  list.push(item);
  await saveData(key, list);
};

export const removeFromListById = async (key, id) => {
  const list = (await getData(key)) || [];
  const filtered = list.filter(item => item.id !== id);
  await saveData(key, filtered);
};

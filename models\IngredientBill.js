// utils/IngredientBill.js

export function createIngredientBill({
  billNumber,
  vendorName,
  vendorPhone = '',
  items = [],
  total = 0,
  paymentMethod = 'cash',
  paymentStatus = 'paid',
  billImage = '',
  notes = ''
}) {
  return {
    id: Date.now().toString(),
    billNumber,
    vendorName,
    vendorPhone,
    items, // each item: { name, quantity, unit, unitPrice, total }
    total,
    paymentMethod,
    paymentStatus,
    date: new Date().toISOString(),
    billImage,
    notes
  };
}

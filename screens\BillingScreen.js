import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Animated,
  ScrollView,
  Modal,
  Image,
  Keyboard,
  TouchableWithoutFeedback,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Platform
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

const { width, height } = Dimensions.get('window');
const isTablet = width > 768;
const isLandscape = width > height;
const SCREEN_PADDING = 12;
const CARD_MARGIN = 6;
const CARD_PADDING = 10;

export default function BillingScreen() {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [cart, setCart] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [checkoutModalVisible, setCheckoutModalVisible] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [recentCustomers, setRecentCustomers] = useState([]);
  const [showCustomerSuggestions, setShowCustomerSuggestions] = useState(false);
  const [recentOrders, setRecentOrders] = useState([]);
  const [showRecentOrders, setShowRecentOrders] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme, userRole, logout } = useContext(AppContext);

  useEffect(() => {
    loadProducts();
    loadRecentCustomers();
    loadRecentOrders();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    // Add keyboard listeners to hide suggestions when keyboard is dismissed
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setShowCustomerSuggestions(false);
    });

    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));
      const recipeData = [];
      const categorySet = new Set(['all']);

      for (let key of recipeKeys) {
        const raw = await AsyncStorage.getItem(key);
        const parsed = JSON.parse(raw);
        const category = parsed.category || 'Uncategorized';
        categorySet.add(category);

        recipeData.push({
          id: key.replace('recipe_', ''),
          name: key.replace('recipe_', ''),
          price: parsed.sellingPrice ? parseFloat(parsed.sellingPrice) : 0,
          ingredients: parsed.ingredients || [],
          category: category,
          image: parsed.image || null,
          popular: parsed.popular || false
        });
      }

      setProducts(recipeData);
      setCategories(Array.from(categorySet));
    } catch (error) {
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setIsLoading(false);
    }
  };

  const loadRecentCustomers = async () => {
    try {
      const data = await AsyncStorage.getItem('sales_bills');
      if (data) {
        const bills = JSON.parse(data);
        const customers = bills
          .filter(bill => bill.customerName && bill.customerName !== 'Walk-in Customer')
          .map(bill => ({
            name: bill.customerName,
            phone: bill.customerPhone || 'N/A'
          }));

        // Remove duplicates by name
        const uniqueCustomers = Array.from(new Map(customers.map(item => [item.name, item])).values());
        setRecentCustomers(uniqueCustomers.slice(0, 10)); // Keep only 10 most recent
      }
    } catch (error) {
      console.error('Failed to load recent customers:', error);
    }
  };

  const loadRecentOrders = async () => {
    try {
      const data = await AsyncStorage.getItem('sales_bills');
      if (data) {
        const bills = JSON.parse(data);
        // Sort by date (newest first) and take the 5 most recent
        const recentBills = bills
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 5);
        setRecentOrders(recentBills);
      }
    } catch (error) {
      console.error('Failed to load recent orders:', error);
    }
  };

  const addToCart = (product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === product.id);
      if (existingItem) {
        return prevCart.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevCart, { ...product, quantity: 1 }];
      }
    });
  };

  const removeFromCart = (productId) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === productId);
      if (existingItem && existingItem.quantity > 1) {
        return prevCart.map(item =>
          item.id === productId
            ? { ...item, quantity: item.quantity - 1 }
            : item
        );
      } else {
        return prevCart.filter(item => item.id !== productId);
      }
    });
  };

  const clearCart = () => {
    setCart([]);
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getCartCount = () => {
    return cart.reduce((count, item) => count + item.quantity, 0);
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to the cart before checkout');
      return;
    }
    setCheckoutModalVisible(true);
  };

  const saveBill = async () => {
    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to the cart before checkout');
      return;
    }

    try {
      setIsSaving(true);

      const billId = Date.now().toString();
      const date = new Date();
      const formattedDate = date.toISOString().split('T')[0];

      const bill = {
        id: billId,
        items: cart.map(item => ({
          id: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          subtotal: item.price * item.quantity
        })),
        total: getCartTotal(),
        date: formattedDate,
        customerName: customerName || 'Walk-in Customer',
        customerPhone: customerPhone || 'N/A',
        paymentMethod,
        createdBy: userRole,
        createdAt: date.toISOString()
      };

      // Save the bill
      const existingBills = JSON.parse(await AsyncStorage.getItem('sales_bills') || '[]');
      existingBills.push(bill);
      await AsyncStorage.setItem('sales_bills', JSON.stringify(existingBills));

      // Update stock levels
      for (const item of cart) {
        const key = `recipe_${item.id}`;
        const recipeRaw = await AsyncStorage.getItem(key);
        if (recipeRaw) {
          const recipe = JSON.parse(recipeRaw);
          if (recipe.ingredients) {
            recipe.ingredients.forEach(ing => {
              const perUnit = ing.amount / recipe.servings;
              ing.stock = (ing.stock || 0) - perUnit * item.quantity;
            });
            await AsyncStorage.setItem(key, JSON.stringify(recipe));
          }
        }
      }

      // Success animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      Alert.alert(
        'Success',
        'Bill saved successfully!',
        [{
          text: 'OK',
          onPress: () => {
            setCheckoutModalVisible(false);
            clearCart();
            setCustomerName('');
            setCustomerPhone('');
            setPaymentMethod('cash');
          }
        }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save bill');
    } finally {
      setIsSaving(false);
    }
  };

  const filteredProducts = products.filter(product => {
    // Filter by search query if present
    const matchesSearch = searchQuery
      ? product.name.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    // Filter by category if not 'all'
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Sort products: popular first, then alphabetically
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (a.popular && !b.popular) return -1;
    if (!a.popular && b.popular) return 1;
    return a.name.localeCompare(b.name);
  });

  const selectCustomer = (customer) => {
    setCustomerName(customer.name);
    setCustomerPhone(customer.phone !== 'N/A' ? customer.phone : '');
    setShowCustomerSuggestions(false);
  };

  const applyRecentOrder = (order) => {
    // Clear current cart and add items from the selected order
    setCart(order.items.map(item => ({
      id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.quantity
    })));
    setShowRecentOrders(false);
  };

  const renderProductItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.productCard, { backgroundColor: theme.surface }]}
      onPress={() => addToCart(item)}
      activeOpacity={0.7}
    >
      {item.popular && (
        <View style={[styles.popularBadge, { backgroundColor: theme.accent }]}>
          <Text style={styles.popularBadgeText}>Popular</Text>
        </View>
      )}

      <View style={styles.productImageContainer}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.productImage} resizeMode="cover" />
        ) : (
          <View style={[styles.productImagePlaceholder, { backgroundColor: theme.card.background }]}>
            <MaterialIcons name="bakery-dining" size={24} color={theme.text.secondary} />
          </View>
        )}
      </View>

      <Text style={[styles.productName, { color: theme.text.primary }]}>{item.name}</Text>
      <Text style={[styles.productPrice, { color: theme.success }]}>₹{item.price.toFixed(2)}</Text>

      <View style={styles.productActions}>
        <View style={styles.quantityButtons}>
          <TouchableOpacity
            style={[styles.quantityButton, { backgroundColor: theme.card.background }]}
            onPress={() => addToCart(item)}
          >
            <Text style={[styles.quantityButtonText, { color: theme.text.primary }]}>+1</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.quantityButton, { backgroundColor: theme.card.background }]}
            onPress={() => {
              // Add 5 of this item at once
              for (let i = 0; i < 5; i++) {
                addToCart(item);
              }
            }}
          >
            <Text style={[styles.quantityButtonText, { color: theme.text.primary }]}>+5</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.primary }]}
          onPress={() => addToCart(item)}
        >
          <MaterialIcons name="add-shopping-cart" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderCartItem = ({ item }) => (
    <View style={[styles.cartItem, { backgroundColor: theme.card.background }]}>
      <View style={styles.cartItemInfo}>
        <Text style={[styles.cartItemName, { color: theme.text.primary }]}>{item.name}</Text>
        <Text style={[styles.cartItemPrice, { color: theme.text.secondary }]}>
          ₹{item.price.toFixed(2)} × {item.quantity}
        </Text>
      </View>
      <Text style={[styles.cartItemTotal, { color: theme.success }]}>
        ₹{(item.price * item.quantity).toFixed(2)}
      </Text>
      <View style={styles.cartItemActions}>
        <TouchableOpacity
          style={[styles.cartActionButton, { backgroundColor: theme.error }]}
          onPress={() => removeFromCart(item.id)}
        >
          <MaterialIcons name="remove" size={18} color="white" />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.cartActionButton, { backgroundColor: theme.primary }]}
          onPress={() => addToCart(item)}
        >
          <MaterialIcons name="add" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  // Determine the number of columns based on screen width and orientation
  const getNumColumns = () => {
    if (isTablet) return isLandscape ? 4 : 3;
    return isLandscape ? 3 : 2;
  };

  // Close all popups when tapping outside
  const handleOutsidePress = () => {
    setShowCustomerSuggestions(false);
    setShowRecentOrders(false);
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <View style={[styles.container, { backgroundColor: theme.background }]}>
          <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
            <View style={styles.header}>
              <Text style={[styles.title, { color: theme.text.primary }]}>Billing System</Text>
              {userRole === 'staff' && (
                <TouchableOpacity
                  style={[styles.logoutButton, { backgroundColor: theme.error }]}
                  onPress={async () => {
                    await logout();
                    navigation.replace('Login');
                  }}
                >
                  <MaterialIcons name="logout" size={20} color="#fff" />
                  <Text style={styles.logoutButtonText}>Logout</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Mobile-friendly layout with conditional rendering based on screen size */}
            {isLandscape ? (
              // Landscape layout (side by side)
              <View style={styles.landscapeContent}>
                {/* Products Section */}
                <View style={styles.productsSection}>
                  <View style={styles.productHeader}>
                    <View style={[styles.searchBar, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                      <MaterialIcons name="search" size={20} color={theme.text.secondary} />
                      <TextInput
                        style={[styles.searchInput, { color: theme.text.primary }]}
                        placeholder="Search products..."
                        placeholderTextColor={theme.text.hint}
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                      />
                      {searchQuery ? (
                        <TouchableOpacity onPress={() => setSearchQuery('')}>
                          <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
                        </TouchableOpacity>
                      ) : null}
                    </View>

                    <TouchableOpacity
                      style={[styles.recentOrdersButton, { backgroundColor: theme.card.statsBlue }]}
                      onPress={() => setShowRecentOrders(!showRecentOrders)}
                    >
                      <MaterialIcons name="history" size={18} color={theme.secondary} />
                      <Text style={[styles.recentOrdersButtonText, { color: theme.secondary }]}>Recent</Text>
                    </TouchableOpacity>
                  </View>

                  {/* Categories Horizontal Scroll */}
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.categoriesContainer}
                    contentContainerStyle={styles.categoriesContent}
                  >
                    {categories.map(category => (
                      <TouchableOpacity
                        key={category}
                        style={[
                          styles.categoryButton,
                          selectedCategory === category && { backgroundColor: theme.primary },
                          { borderColor: theme.border }
                        ]}
                        onPress={() => setSelectedCategory(category)}
                      >
                        <Text
                          style={[
                            styles.categoryButtonText,
                            { color: selectedCategory === category ? 'white' : theme.text.primary }
                          ]}
                        >
                          {category === 'all' ? 'All' : category}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>

                  {/* Recent Orders Popup */}
                  {showRecentOrders && (
                    <View style={[styles.recentOrdersPopup, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                      <View style={styles.recentOrdersHeader}>
                        <Text style={[styles.recentOrdersTitle, { color: theme.text.primary }]}>Recent Orders</Text>
                        <TouchableOpacity onPress={() => setShowRecentOrders(false)}>
                          <MaterialIcons name="close" size={20} color={theme.text.secondary} />
                        </TouchableOpacity>
                      </View>

                      {recentOrders.length === 0 ? (
                        <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No recent orders found</Text>
                      ) : (
                        <ScrollView style={styles.recentOrdersList}>
                          {recentOrders.map((order, index) => (
                            <TouchableOpacity
                              key={order.id}
                              style={[styles.recentOrderItem, { borderBottomColor: theme.border }]}
                              onPress={() => applyRecentOrder(order)}
                            >
                              <View style={styles.recentOrderInfo}>
                                <Text style={[styles.recentOrderCustomer, { color: theme.text.primary }]}>
                                  {order.customerName}
                                </Text>
                                <Text style={[styles.recentOrderDate, { color: theme.text.secondary }]}>
                                  {new Date(order.createdAt).toLocaleDateString()}
                                </Text>
                              </View>
                              <View style={styles.recentOrderDetails}>
                                <Text style={[styles.recentOrderItems, { color: theme.text.secondary }]}>
                                  {order.items.length} items
                                </Text>
                                <Text style={[styles.recentOrderTotal, { color: theme.success }]}>
                                  ₹{order.total.toFixed(2)}
                                </Text>
                              </View>
                              <MaterialIcons name="add-shopping-cart" size={18} color={theme.primary} />
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                      )}
                    </View>
                  )}

                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="large" color={theme.primary} />
                      <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading products...</Text>
                    </View>
                  ) : sortedProducts.length === 0 ? (
                    <View style={styles.emptyContainer}>
                      <MaterialIcons name="inventory" size={48} color={theme.text.disabled} />
                      <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
                        {searchQuery ? 'No products found matching your search' : 'No products available'}
                      </Text>
                    </View>
                  ) : (
                    <FlatList
                      data={sortedProducts}
                      renderItem={renderProductItem}
                      keyExtractor={item => item.id}
                      numColumns={getNumColumns()}
                      contentContainerStyle={styles.productsList}
                      initialNumToRender={12}
                      maxToRenderPerBatch={20}
                      windowSize={10}
                    />
                  )}
                </View>

                {/* Cart Section */}
                <View style={[styles.cartSection, { backgroundColor: theme.surface }]}>
                </View>
              </View>
            ) : (
              // Portrait layout (stacked)
              <View style={styles.portraitContent}>
                {/* Search and Categories */}
                <View style={styles.productHeader}>
                  <View style={[styles.searchBar, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                    <MaterialIcons name="search" size={20} color={theme.text.secondary} />
                    <TextInput
                      style={[styles.searchInput, { color: theme.text.primary }]}
                      placeholder="Search products..."
                      placeholderTextColor={theme.text.hint}
                      value={searchQuery}
                      onChangeText={setSearchQuery}
                    />
                    {searchQuery ? (
                      <TouchableOpacity onPress={() => setSearchQuery('')}>
                        <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
                      </TouchableOpacity>
                    ) : null}
                  </View>

                  <TouchableOpacity
                    style={[styles.recentOrdersButton, { backgroundColor: theme.card.statsBlue }]}
                    onPress={() => setShowRecentOrders(!showRecentOrders)}
                  >
                    <MaterialIcons name="history" size={18} color={theme.secondary} />
                    <Text style={[styles.recentOrdersButtonText, { color: theme.secondary }]}>Recent</Text>
                  </TouchableOpacity>
                </View>

                {/* Categories */}
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.categoriesContainer}
                  contentContainerStyle={styles.categoriesContent}
                >
                  {categories.map(category => (
                    <TouchableOpacity
                      key={category}
                      style={[
                        styles.categoryButton,
                        selectedCategory === category && { backgroundColor: theme.primary },
                        { borderColor: theme.border }
                      ]}
                      onPress={() => setSelectedCategory(category)}
                    >
                      <Text
                        style={[
                          styles.categoryButtonText,
                          { color: selectedCategory === category ? 'white' : theme.text.primary }
                        ]}
                      >
                        {category === 'all' ? 'All' : category}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                {/* Recent Orders Popup */}
                {showRecentOrders && (
                  <View style={[styles.recentOrdersPopup, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                    <View style={styles.recentOrdersHeader}>
                      <Text style={[styles.recentOrdersTitle, { color: theme.text.primary }]}>Recent Orders</Text>
                      <TouchableOpacity onPress={() => setShowRecentOrders(false)}>
                        <MaterialIcons name="close" size={20} color={theme.text.secondary} />
                      </TouchableOpacity>
                    </View>

                    {recentOrders.length === 0 ? (
                      <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No recent orders found</Text>
                    ) : (
                      <ScrollView style={styles.recentOrdersList}>
                        {recentOrders.map((order, index) => (
                          <TouchableOpacity
                            key={order.id}
                            style={[styles.recentOrderItem, { borderBottomColor: theme.border }]}
                            onPress={() => applyRecentOrder(order)}
                          >
                            <View style={styles.recentOrderInfo}>
                              <Text style={[styles.recentOrderCustomer, { color: theme.text.primary }]}>
                                {order.customerName}
                              </Text>
                              <Text style={[styles.recentOrderDate, { color: theme.text.secondary }]}>
                                {new Date(order.createdAt).toLocaleDateString()}
                              </Text>
                            </View>
                            <View style={styles.recentOrderDetails}>
                              <Text style={[styles.recentOrderItems, { color: theme.text.secondary }]}>
                                {order.items.length} items
                              </Text>
                              <Text style={[styles.recentOrderTotal, { color: theme.success }]}>
                                ₹{order.total.toFixed(2)}
                              </Text>
                            </View>
                            <MaterialIcons name="add-shopping-cart" size={18} color={theme.primary} />
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    )}
                  </View>
                )}

                {/* Products Grid */}
                <View style={styles.productsContainer}>
                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="large" color={theme.primary} />
                      <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading products...</Text>
                    </View>
                  ) : sortedProducts.length === 0 ? (
                    <View style={styles.emptyContainer}>
                      <MaterialIcons name="inventory" size={48} color={theme.text.disabled} />
                      <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
                        {searchQuery ? 'No products found matching your search' : 'No products available'}
                      </Text>
                    </View>
                  ) : (
                    <FlatList
                      data={sortedProducts}
                      renderItem={renderProductItem}
                      keyExtractor={item => item.id}
                      numColumns={2}
                      contentContainerStyle={styles.productsList}
                      initialNumToRender={8}
                      maxToRenderPerBatch={16}
                      windowSize={5}
                    />
                  )}
                </View>

                {/* Cart Section */}
                <View style={[styles.cartSection, { backgroundColor: theme.surface }]}>
            <View style={[styles.cartHeader, { borderBottomColor: theme.border }]}>
              <Text style={[styles.cartTitle, { color: theme.text.primary }]}>
                Shopping Cart ({getCartCount()} items)
              </Text>
              {cart.length > 0 && (
                <TouchableOpacity onPress={clearCart}>
                  <MaterialIcons name="remove-shopping-cart" size={24} color={theme.error} />
                </TouchableOpacity>
              )}
            </View>

            {cart.length === 0 ? (
              <View style={styles.emptyCartContainer}>
                <MaterialIcons name="shopping-cart" size={64} color={theme.text.disabled} />
                <Text style={[styles.emptyCartText, { color: theme.text.secondary }]}>
                  Your cart is empty
                </Text>
                <Text style={[styles.emptyCartSubtext, { color: theme.text.hint }]}>
                  Add products from the left panel
                </Text>
              </View>
            ) : (
              <FlatList
                data={cart}
                renderItem={renderCartItem}
                keyExtractor={item => item.id}
                style={styles.cartList}
              />
            )}

            <View style={[styles.cartFooter, { borderTopColor: theme.border }]}>
              <View style={styles.cartTotalRow}>
                <Text style={[styles.cartTotalLabel, { color: theme.text.primary }]}>Total:</Text>
                <Text style={[styles.cartTotal, { color: theme.success }]}>
                  ₹{getCartTotal().toFixed(2)}
                </Text>
              </View>
              <TouchableOpacity
                style={[styles.checkoutButton, { backgroundColor: theme.primary }]}
                onPress={handleCheckout}
                disabled={cart.length === 0}
              >
                <MaterialIcons name="payment" size={20} color="white" />
                <Text style={styles.checkoutButtonText}>Checkout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Checkout Modal */}
        <Modal
          visible={checkoutModalVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setCheckoutModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
              <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Checkout</Text>
                <TouchableOpacity onPress={() => setCheckoutModalVisible(false)}>
                  <MaterialIcons name="close" size={24} color={theme.text.primary} />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.modalBody}>
                <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Order Summary</Text>

                {cart.map(item => (
                  <View key={item.id} style={[styles.summaryItem, { borderBottomColor: theme.border }]}>
                    <View style={styles.summaryItemInfo}>
                      <Text style={[styles.summaryItemName, { color: theme.text.primary }]}>
                        {item.name} × {item.quantity}
                      </Text>
                      <Text style={[styles.summaryItemPrice, { color: theme.text.secondary }]}>
                        ₹{item.price.toFixed(2)} each
                      </Text>
                    </View>
                    <Text style={[styles.summaryItemTotal, { color: theme.success }]}>
                      ₹{(item.price * item.quantity).toFixed(2)}
                    </Text>
                  </View>
                ))}

                <View style={[styles.totalRow, { borderTopColor: theme.border }]}>
                  <Text style={[styles.totalLabel, { color: theme.text.primary }]}>Total Amount:</Text>
                  <Text style={[styles.totalAmount, { color: theme.success }]}>
                    ₹{getCartTotal().toFixed(2)}
                  </Text>
                </View>

                <Text style={[styles.sectionTitle, { color: theme.text.primary, marginTop: 20 }]}>
                  Customer Information
                </Text>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Customer Name:</Text>
                  <View style={styles.customerInputContainer}>
                    <TextInput
                      style={[styles.input, {
                        backgroundColor: theme.background,
                        borderColor: theme.border,
                        color: theme.text.primary
                      }]}
                      placeholder="Enter customer name (optional)"
                      placeholderTextColor={theme.text.hint}
                      value={customerName}
                      onChangeText={(text) => {
                        setCustomerName(text);
                        setShowCustomerSuggestions(text.length > 0);
                      }}
                      onFocus={() => {
                        if (customerName.length > 0) {
                          setShowCustomerSuggestions(true);
                        }
                      }}
                    />
                    {customerName.length > 0 && (
                      <TouchableOpacity
                        style={styles.clearInputButton}
                        onPress={() => {
                          setCustomerName('');
                          setCustomerPhone('');
                          setShowCustomerSuggestions(false);
                        }}
                      >
                        <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
                      </TouchableOpacity>
                    )}
                  </View>

                  {/* Customer Suggestions */}
                  {showCustomerSuggestions && recentCustomers.length > 0 && (
                    <View style={[styles.suggestionsList, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                      {recentCustomers
                        .filter(c => c.name.toLowerCase().includes(customerName.toLowerCase()))
                        .slice(0, 5)
                        .map((customer, index) => (
                          <TouchableOpacity
                            key={index}
                            style={[styles.suggestionItem, index !== 0 && { borderTopWidth: 1, borderTopColor: theme.border }]}
                            onPress={() => selectCustomer(customer)}
                          >
                            <MaterialIcons name="person" size={20} color={theme.text.secondary} />
                            <View style={styles.suggestionInfo}>
                              <Text style={[styles.suggestionName, { color: theme.text.primary }]}>{customer.name}</Text>
                              {customer.phone !== 'N/A' && (
                                <Text style={[styles.suggestionPhone, { color: theme.text.secondary }]}>{customer.phone}</Text>
                              )}
                            </View>
                          </TouchableOpacity>
                        ))}
                    </View>
                  )}
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Phone Number:</Text>
                  <TextInput
                    style={[styles.input, {
                      backgroundColor: theme.background,
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Enter phone number (optional)"
                    placeholderTextColor={theme.text.hint}
                    value={customerPhone}
                    onChangeText={setCustomerPhone}
                    keyboardType="phone-pad"
                  />
                </View>

                <Text style={[styles.sectionTitle, { color: theme.text.primary, marginTop: 20 }]}>
                  Payment Method
                </Text>

                <View style={styles.paymentOptions}>
                  <TouchableOpacity
                    style={[
                      styles.paymentOption,
                      paymentMethod === 'cash' && {
                        backgroundColor: theme.card.statsGreen,
                        borderColor: theme.success
                      }
                    ]}
                    onPress={() => setPaymentMethod('cash')}
                  >
                    <MaterialIcons
                      name="payments"
                      size={24}
                      color={paymentMethod === 'cash' ? theme.success : theme.text.secondary}
                    />
                    <Text style={[
                      styles.paymentOptionText,
                      { color: paymentMethod === 'cash' ? theme.success : theme.text.secondary }
                    ]}>
                      Cash
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentOption,
                      paymentMethod === 'card' && {
                        backgroundColor: theme.card.statsBlue,
                        borderColor: theme.secondary
                      }
                    ]}
                    onPress={() => setPaymentMethod('card')}
                  >
                    <MaterialIcons
                      name="credit-card"
                      size={24}
                      color={paymentMethod === 'card' ? theme.secondary : theme.text.secondary}
                    />
                    <Text style={[
                      styles.paymentOptionText,
                      { color: paymentMethod === 'card' ? theme.secondary : theme.text.secondary }
                    ]}>
                      Card
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentOption,
                      paymentMethod === 'upi' && {
                        backgroundColor: theme.card.statsYellow,
                        borderColor: theme.accent
                      }
                    ]}
                    onPress={() => setPaymentMethod('upi')}
                  >
                    <MaterialIcons
                      name="smartphone"
                      size={24}
                      color={paymentMethod === 'upi' ? theme.accent : theme.text.secondary}
                    />
                    <Text style={[
                      styles.paymentOptionText,
                      { color: paymentMethod === 'upi' ? theme.accent : theme.text.secondary }
                    ]}>
                      UPI
                    </Text>
                  </TouchableOpacity>
                </View>
              </ScrollView>

              <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                <TouchableOpacity
                  style={[styles.cancelButton, { borderColor: theme.border }]}
                  onPress={() => setCheckoutModalVisible(false)}
                >
                  <Text style={[styles.cancelButtonText, { color: theme.text.primary }]}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.confirmButton, { backgroundColor: theme.primary }]}
                  onPress={saveBill}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <ActivityIndicator color="white" size="small" />
                  ) : (
                    <>
                      <MaterialIcons name="receipt" size={20} color="white" />
                      <Text style={styles.confirmButtonText}>Complete Sale</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  // Main layout styles
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  logoutButtonText: {
    color: 'white',
    marginLeft: 6,
    fontWeight: '500',
    fontSize: 14,
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  productsSection: {
    flex: 2,
    marginRight: 16,
  },
  cartSection: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    height: 48,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  productsList: {
    paddingBottom: 16,
  },
  productCard: {
    flex: 1,
    margin: 8,
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  // Product card styles
  productImageContainer: {
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 8,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  productImagePlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  popularBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  popularBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  productActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  quantityButtons: {
    flexDirection: 'row',
  },
  quantityButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 4,
  },
  quantityButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Category styles
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categoriesContent: {
    paddingBottom: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  recentOrdersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  recentOrdersButtonText: {
    marginLeft: 4,
    fontWeight: '500',
    fontSize: 14,
  },

  // Recent orders popup
  recentOrdersPopup: {
    position: 'absolute',
    top: 120,
    right: 0,
    width: 300,
    maxHeight: 400,
    borderRadius: 8,
    borderWidth: 1,
    zIndex: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  recentOrdersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  recentOrdersTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  recentOrderItem: {
    padding: 12,
    borderBottomWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  recentOrderInfo: {
    flex: 1,
  },
  recentOrderCustomer: {
    fontSize: 14,
    fontWeight: '500',
  },
  recentOrderDate: {
    fontSize: 12,
    marginTop: 4,
  },
  recentOrderDetails: {
    marginRight: 8,
    alignItems: 'flex-end',
  },
  recentOrderItems: {
    fontSize: 12,
  },
  recentOrderTotal: {
    fontSize: 14,
    fontWeight: 'bold',
  },

  // Customer suggestions
  customerInputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  clearInputButton: {
    position: 'absolute',
    right: 12,
    padding: 4,
  },
  suggestionsList: {
    position: 'absolute',
    top: 76,
    left: 0,
    right: 0,
    borderWidth: 1,
    borderRadius: 8,
    maxHeight: 200,
    zIndex: 1000,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  suggestionInfo: {
    marginLeft: 8,
    flex: 1,
  },
  suggestionName: {
    fontSize: 14,
    fontWeight: '500',
  },
  suggestionPhone: {
    fontSize: 12,
    marginTop: 2,
  },
  cartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  cartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cartList: {
    flex: 1,
  },
  cartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  cartItemPrice: {
    fontSize: 14,
    marginTop: 4,
  },
  cartItemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  cartItemActions: {
    flexDirection: 'row',
  },
  cartActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  cartFooter: {
    padding: 16,
    borderTopWidth: 1,
  },
  cartTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cartTotalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cartTotal: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  checkoutButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyCartText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  emptyCartSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    maxHeight: '80%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
    maxHeight: 500,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  summaryItemInfo: {
    flex: 1,
  },
  summaryItemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryItemPrice: {
    fontSize: 14,
    marginTop: 4,
  },
  summaryItemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  paymentOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  paymentOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  paymentOptionText: {
    marginTop: 8,
    fontWeight: '500',
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    padding: 12,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

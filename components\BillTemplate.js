import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

export default function BillTemplate({ 
  billData, 
  shopProfile, 
  theme, 
  printMode = false 
}) {
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return dateString;
    }
  };

  // Get a random quote from the shop profile
  const getRandomQuote = () => {
    if (!shopProfile?.quotes || shopProfile.quotes.length === 0) {
      return "Thank you for your business!";
    }
    const randomIndex = Math.floor(Math.random() * shopProfile.quotes.length);
    return shopProfile.quotes[randomIndex];
  };

  return (
    <View style={[
      styles.billContainer, 
      printMode ? styles.printContainer : { backgroundColor: theme.surface }
    ]}>
      {/* Shop Header */}
      <View style={styles.shopHeader}>
        <Text style={[
          styles.shopName, 
          printMode ? styles.printText : { color: theme.text.primary }
        ]}>
          {shopProfile?.shopName || 'Bakery Shop'}
        </Text>
        
        {shopProfile?.address && (
          <Text style={[
            styles.shopAddress, 
            printMode ? styles.printText : { color: theme.text.secondary }
          ]}>
            {shopProfile.address}
          </Text>
        )}
        
        <View style={styles.shopContact}>
          {shopProfile?.phone && (
            <Text style={[
              styles.shopContactText, 
              printMode ? styles.printText : { color: theme.text.secondary }
            ]}>
              Phone: {shopProfile.phone}
            </Text>
          )}
          
          {shopProfile?.email && (
            <Text style={[
              styles.shopContactText, 
              printMode ? styles.printText : { color: theme.text.secondary }
            ]}>
              Email: {shopProfile.email}
            </Text>
          )}
        </View>
        
        {shopProfile?.gstNumber && (
          <Text style={[
            styles.shopGst, 
            printMode ? styles.printText : { color: theme.text.secondary }
          ]}>
            GST No: {shopProfile.gstNumber}
          </Text>
        )}
      </View>

      {/* Bill Info */}
      <View style={[
        styles.billInfo, 
        printMode ? styles.printBorder : { borderColor: theme.border }
      ]}>
        <View style={styles.billInfoRow}>
          <Text style={[
            styles.billInfoLabel, 
            printMode ? styles.printText : { color: theme.text.secondary }
          ]}>
            Bill No:
          </Text>
          <Text style={[
            styles.billInfoValue, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            {billData.id}
          </Text>
        </View>
        
        <View style={styles.billInfoRow}>
          <Text style={[
            styles.billInfoLabel, 
            printMode ? styles.printText : { color: theme.text.secondary }
          ]}>
            Date:
          </Text>
          <Text style={[
            styles.billInfoValue, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            {formatDate(billData.createdAt)}
          </Text>
        </View>
        
        <View style={styles.billInfoRow}>
          <Text style={[
            styles.billInfoLabel, 
            printMode ? styles.printText : { color: theme.text.secondary }
          ]}>
            Customer:
          </Text>
          <Text style={[
            styles.billInfoValue, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            {billData.customerName}
          </Text>
        </View>
        
        {billData.customerPhone && billData.customerPhone !== 'N/A' && (
          <View style={styles.billInfoRow}>
            <Text style={[
              styles.billInfoLabel, 
              printMode ? styles.printText : { color: theme.text.secondary }
            ]}>
              Phone:
            </Text>
            <Text style={[
              styles.billInfoValue, 
              printMode ? styles.printText : { color: theme.text.primary }
            ]}>
              {billData.customerPhone}
            </Text>
          </View>
        )}
        
        <View style={styles.billInfoRow}>
          <Text style={[
            styles.billInfoLabel, 
            printMode ? styles.printText : { color: theme.text.secondary }
          ]}>
            Payment:
          </Text>
          <Text style={[
            styles.billInfoValue, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            {billData.paymentMethod.charAt(0).toUpperCase() + billData.paymentMethod.slice(1)}
          </Text>
        </View>
      </View>

      {/* Items Table */}
      <View style={styles.itemsTable}>
        {/* Table Header */}
        <View style={[
          styles.tableHeader, 
          printMode ? styles.printBorder : { backgroundColor: theme.card.background, borderColor: theme.border }
        ]}>
          <Text style={[
            styles.tableHeaderCell, 
            styles.itemCell, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            Item
          </Text>
          <Text style={[
            styles.tableHeaderCell, 
            styles.qtyCell, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            Qty
          </Text>
          <Text style={[
            styles.tableHeaderCell, 
            styles.priceCell, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            Price
          </Text>
          <Text style={[
            styles.tableHeaderCell, 
            styles.totalCell, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            Total
          </Text>
        </View>

        {/* Table Body */}
        {billData.items.map((item, index) => (
          <View 
            key={index} 
            style={[
              styles.tableRow, 
              printMode ? styles.printBorder : { borderColor: theme.border }
            ]}
          >
            <Text style={[
              styles.tableCell, 
              styles.itemCell, 
              printMode ? styles.printText : { color: theme.text.primary }
            ]}>
              {item.name}
            </Text>
            <Text style={[
              styles.tableCell, 
              styles.qtyCell, 
              printMode ? styles.printText : { color: theme.text.primary }
            ]}>
              {item.quantity}
            </Text>
            <Text style={[
              styles.tableCell, 
              styles.priceCell, 
              printMode ? styles.printText : { color: theme.text.primary }
            ]}>
              ₹{item.price.toFixed(2)}
            </Text>
            <Text style={[
              styles.tableCell, 
              styles.totalCell, 
              printMode ? styles.printText : { color: theme.text.primary }
            ]}>
              ₹{item.subtotal.toFixed(2)}
            </Text>
          </View>
        ))}

        {/* Table Footer */}
        <View style={[
          styles.tableFooter, 
          printMode ? styles.printBorder : { borderColor: theme.border }
        ]}>
          <Text style={[
            styles.tableFooterLabel, 
            printMode ? styles.printText : { color: theme.text.primary }
          ]}>
            Total Amount:
          </Text>
          <Text style={[
            styles.tableFooterValue, 
            printMode ? styles.printText : { color: theme.success }
          ]}>
            ₹{billData.total.toFixed(2)}
          </Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[
          styles.footerQuote, 
          printMode ? styles.printText : { color: theme.text.secondary }
        ]}>
          "{getRandomQuote()}"
        </Text>
        
        <Text style={[
          styles.footerText, 
          printMode ? styles.printText : { color: theme.text.hint }
        ]}>
          This is a computer-generated bill and does not require a signature.
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  billContainer: {
    padding: 16,
    borderRadius: 8,
  },
  printContainer: {
    backgroundColor: 'white',
  },
  printText: {
    color: 'black',
  },
  printBorder: {
    borderColor: '#ddd',
  },
  shopHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  shopName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  shopAddress: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 4,
  },
  shopContact: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 4,
  },
  shopContactText: {
    fontSize: 12,
    marginHorizontal: 4,
  },
  shopGst: {
    fontSize: 12,
  },
  billInfo: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  billInfoRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  billInfoLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 80,
  },
  billInfoValue: {
    fontSize: 14,
    flex: 1,
  },
  itemsTable: {
    marginBottom: 16,
  },
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    paddingVertical: 8,
  },
  tableHeaderCell: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    paddingVertical: 8,
  },
  tableCell: {
    fontSize: 14,
  },
  itemCell: {
    flex: 3,
  },
  qtyCell: {
    flex: 1,
    textAlign: 'center',
  },
  priceCell: {
    flex: 2,
    textAlign: 'right',
  },
  totalCell: {
    flex: 2,
    textAlign: 'right',
  },
  tableFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    marginTop: 8,
  },
  tableFooterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  tableFooterValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
  },
  footerQuote: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 8,
  },
  footerText: {
    fontSize: 10,
    textAlign: 'center',
  },
});

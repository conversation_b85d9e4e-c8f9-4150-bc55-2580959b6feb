# Production Build Instructions

This document provides instructions for creating production builds of the Bakery Tracker app.

## Prerequisites

1. An Expo account (create one at https://expo.dev/signup)
2. For iOS builds: An Apple Developer account
3. For Android builds: No additional accounts needed

## Setup

1. Install the EAS CLI globally:
```bash
npm install -g eas-cli
```

2. Log in to your Expo account:
```bash
eas login
```

3. Configure your project for builds:
```bash
eas build:configure
```

## Creating Builds

### Android Build

To create an Android build:

```bash
eas build --platform android
```

This will:
1. Create a new build on the Expo servers
2. Generate an Android App Bundle (.aab) file
3. Provide a download link when complete

### iOS Build

To create an iOS build (requires Apple Developer account):

```bash
eas build --platform ios
```

This will:
1. Create a new build on the Expo servers
2. Generate an iOS archive
3. Provide a download link when complete

## Testing Firebase Features

The production build will have full access to all Firebase features:

1. **Firebase Analytics**: Will track user behavior and events
2. **Firebase Authentication**: Will handle user login and registration
3. **Firebase Realtime Database**: Will store and sync data in real-time
4. **Firebase Storage**: Will store images and other files
5. **Firebase Remote Config**: Will allow changing app behavior without updates

## Deploying to App Stores

### Google Play Store

1. Create a Google Play Developer account
2. Create a new app in the Google Play Console
3. Upload the .aab file from your EAS build
4. Complete the store listing and release the app

### Apple App Store

1. Create an app in App Store Connect
2. Use Transporter to upload the .ipa file from your EAS build
3. Complete the store listing and submit for review

## Troubleshooting

If you encounter issues with the build process:

1. Check the EAS build logs for errors
2. Ensure all dependencies are properly installed
3. Verify your Expo account has the necessary permissions
4. For iOS builds, ensure your Apple Developer account is active

For more information, see the [Expo Build Documentation](https://docs.expo.dev/build/introduction/).

import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Animated,
  ScrollView,
  Modal,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Share
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import BillTemplate from '../components/BillTemplate';
import PrintService from '../utils/PrintService';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function CartScreen() {
  const [cart, setCart] = useState([]);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [checkoutModalVisible, setCheckoutModalVisible] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [recentCustomers, setRecentCustomers] = useState([]);
  const [showCustomerSuggestions, setShowCustomerSuggestions] = useState(false);
  const [shopProfile, setShopProfile] = useState(null);
  const [billPreviewVisible, setBillPreviewVisible] = useState(false);
  const [currentBill, setCurrentBill] = useState(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme, userRole } = useContext(AppContext);

  useEffect(() => {
    loadCart();
    loadRecentCustomers();
    loadShopProfile();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadCart = async () => {
    try {
      const cartData = await AsyncStorage.getItem('current_cart');
      if (cartData) {
        setCart(JSON.parse(cartData));
      }
    } catch (error) {
      console.error('Failed to load cart:', error);
    }
  };

  const saveCart = async (updatedCart) => {
    try {
      await AsyncStorage.setItem('current_cart', JSON.stringify(updatedCart));
    } catch (error) {
      console.error('Failed to save cart:', error);
    }
  };

  const loadRecentCustomers = async () => {
    try {
      const data = await AsyncStorage.getItem('sales_bills');
      if (data) {
        const bills = JSON.parse(data);
        const customers = bills
          .filter(bill => bill.customerName && bill.customerName !== 'Walk-in Customer')
          .map(bill => ({
            name: bill.customerName,
            phone: bill.customerPhone || 'N/A'
          }));

        // Remove duplicates by name
        const uniqueCustomers = Array.from(new Map(customers.map(item => [item.name, item])).values());
        setRecentCustomers(uniqueCustomers.slice(0, 10)); // Keep only 10 most recent
      }
    } catch (error) {
      console.error('Failed to load recent customers:', error);
    }
  };

  const loadShopProfile = async () => {
    try {
      const shopProfileData = await AsyncStorage.getItem('shop_profile');
      if (shopProfileData) {
        setShopProfile(JSON.parse(shopProfileData));
      }
    } catch (error) {
      console.error('Failed to load shop profile:', error);
    }
  };

  const addToCart = (product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === product.id);
      let updatedCart;

      if (existingItem) {
        updatedCart = prevCart.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        updatedCart = [...prevCart, { ...product, quantity: 1 }];
      }

      saveCart(updatedCart);
      return updatedCart;
    });
  };

  const removeFromCart = (productId) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === productId);
      let updatedCart;

      if (existingItem && existingItem.quantity > 1) {
        updatedCart = prevCart.map(item =>
          item.id === productId
            ? { ...item, quantity: item.quantity - 1 }
            : item
        );
      } else {
        updatedCart = prevCart.filter(item => item.id !== productId);
      }

      saveCart(updatedCart);
      return updatedCart;
    });
  };

  const clearCart = async () => {
    await AsyncStorage.removeItem('current_cart');
    setCart([]);
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getCartCount = () => {
    return cart.reduce((count, item) => count + item.quantity, 0);
  };

  const selectCustomer = (customer) => {
    setCustomerName(customer.name);
    setCustomerPhone(customer.phone !== 'N/A' ? customer.phone : '');
    setShowCustomerSuggestions(false);
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to the cart before checkout');
      return;
    }
    setCheckoutModalVisible(true);
  };

  const saveBill = async () => {
    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to the cart before checkout');
      return;
    }

    try {
      setIsSaving(true);

      const billId = Date.now().toString();
      const date = new Date();
      const formattedDate = date.toISOString().split('T')[0];

      const bill = {
        id: billId,
        items: cart.map(item => ({
          id: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          subtotal: item.price * item.quantity
        })),
        total: getCartTotal(),
        date: formattedDate,
        customerName: customerName || 'Walk-in Customer',
        customerPhone: customerPhone || 'N/A',
        paymentMethod,
        createdBy: userRole,
        createdAt: date.toISOString()
      };

      // Save the bill
      const existingBills = JSON.parse(await AsyncStorage.getItem('sales_bills') || '[]');
      existingBills.push(bill);
      await AsyncStorage.setItem('sales_bills', JSON.stringify(existingBills));

      // Update stock levels
      for (const item of cart) {
        const key = `recipe_${item.id}`;
        const recipeRaw = await AsyncStorage.getItem(key);
        if (recipeRaw) {
          const recipe = JSON.parse(recipeRaw);
          if (recipe.ingredients) {
            recipe.ingredients.forEach(ing => {
              const perUnit = ing.amount / recipe.servings;
              ing.stock = (ing.stock || 0) - perUnit * item.quantity;
            });
            await AsyncStorage.setItem(key, JSON.stringify(recipe));
          }
        }
      }

      // Set current bill for preview
      setCurrentBill(bill);

      // Clear the cart
      await clearCart();

      // Show bill preview
      setCheckoutModalVisible(false);
      setBillPreviewVisible(true);
    } catch (error) {
      Alert.alert('Error', 'Failed to save bill');
    } finally {
      setIsSaving(false);
    }
  };

  const renderCartItem = ({ item }) => (
    <View style={[styles.cartItem, { backgroundColor: theme.card.background }]}>
      <View style={styles.cartItemInfo}>
        <Text style={[styles.cartItemName, { color: theme.text.primary }]}>{item.name}</Text>
        <Text style={[styles.cartItemPrice, { color: theme.text.secondary }]}>
          ₹{item.price.toFixed(2)} × {item.quantity}
        </Text>
      </View>
      <Text style={[styles.cartItemTotal, { color: theme.success }]}>
        ₹{(item.price * item.quantity).toFixed(2)}
      </Text>
      <View style={styles.cartItemActions}>
        <TouchableOpacity
          style={[styles.cartActionButton, { backgroundColor: theme.error }]}
          onPress={() => removeFromCart(item.id)}
        >
          <MaterialIcons name="remove" size={18} color="white" />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.cartActionButton, { backgroundColor: theme.primary }]}
          onPress={() => addToCart(item)}
        >
          <MaterialIcons name="add" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
          {/* Cart Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.text.primary }]}>
              Shopping Cart ({getCartCount()} items)
            </Text>
            {cart.length > 0 && (
              <TouchableOpacity
                style={[styles.clearButton, { borderColor: theme.error }]}
                onPress={() => {
                  Alert.alert(
                    'Clear Cart',
                    'Are you sure you want to clear the cart?',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      { text: 'Clear', onPress: clearCart, style: 'destructive' }
                    ]
                  );
                }}
              >
                <MaterialIcons name="remove-shopping-cart" size={18} color={theme.error} />
                <Text style={[styles.clearButtonText, { color: theme.error }]}>Clear</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Cart Items */}
          {cart.length === 0 ? (
            <View style={styles.emptyCartContainer}>
              <MaterialIcons name="shopping-cart" size={64} color={theme.text.disabled} />
              <Text style={[styles.emptyCartText, { color: theme.text.secondary }]}>
                Your cart is empty
              </Text>
              <Text style={[styles.emptyCartSubtext, { color: theme.text.hint }]}>
                Add products from the product selection screen
              </Text>
              <TouchableOpacity
                style={[styles.browseButton, { backgroundColor: theme.primary }]}
                onPress={() => navigation.navigate('ProductSelection')}
              >
                <MaterialIcons name="add-shopping-cart" size={20} color="white" />
                <Text style={styles.browseButtonText}>Browse Products</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={cart}
              renderItem={renderCartItem}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.cartList}
            />
          )}

          {/* Cart Footer */}
          {cart.length > 0 && (
            <View style={[styles.cartFooter, { borderTopColor: theme.border }]}>
              <View style={styles.cartTotalRow}>
                <Text style={[styles.cartTotalLabel, { color: theme.text.primary }]}>Total:</Text>
                <Text style={[styles.cartTotal, { color: theme.success }]}>
                  ₹{getCartTotal().toFixed(2)}
                </Text>
              </View>
              <TouchableOpacity
                style={[styles.checkoutButton, { backgroundColor: theme.primary }]}
                onPress={handleCheckout}
              >
                <MaterialIcons name="payment" size={20} color="white" />
                <Text style={styles.checkoutButtonText}>Checkout</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Checkout Modal */}
          <Modal
            visible={checkoutModalVisible}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setCheckoutModalVisible(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                  <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Checkout</Text>
                  <TouchableOpacity onPress={() => setCheckoutModalVisible(false)}>
                    <MaterialIcons name="close" size={24} color={theme.text.primary} />
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.modalBody}>
                  <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Order Summary</Text>

                  {cart.map(item => (
                    <View key={item.id} style={[styles.summaryItem, { borderBottomColor: theme.border }]}>
                      <View style={styles.summaryItemInfo}>
                        <Text style={[styles.summaryItemName, { color: theme.text.primary }]}>
                          {item.name} × {item.quantity}
                        </Text>
                        <Text style={[styles.summaryItemPrice, { color: theme.text.secondary }]}>
                          ₹{item.price.toFixed(2)} each
                        </Text>
                      </View>
                      <Text style={[styles.summaryItemTotal, { color: theme.success }]}>
                        ₹{(item.price * item.quantity).toFixed(2)}
                      </Text>
                    </View>
                  ))}

                  <View style={[styles.totalRow, { borderTopColor: theme.border }]}>
                    <Text style={[styles.totalLabel, { color: theme.text.primary }]}>Total Amount:</Text>
                    <Text style={[styles.totalAmount, { color: theme.success }]}>
                      ₹{getCartTotal().toFixed(2)}
                    </Text>
                  </View>

                  <Text style={[styles.sectionTitle, { color: theme.text.primary, marginTop: 20 }]}>
                    Customer Information
                  </Text>

                  <View style={styles.inputContainer}>
                    <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Customer Name:</Text>
                    <View style={styles.customerInputContainer}>
                      <TextInput
                        style={[styles.input, {
                          backgroundColor: theme.background,
                          borderColor: theme.border,
                          color: theme.text.primary
                        }]}
                        placeholder="Enter customer name (optional)"
                        placeholderTextColor={theme.text.hint}
                        value={customerName}
                        onChangeText={(text) => {
                          setCustomerName(text);
                          setShowCustomerSuggestions(text.length > 0);
                        }}
                        onFocus={() => {
                          if (customerName.length > 0) {
                            setShowCustomerSuggestions(true);
                          }
                        }}
                      />
                      {customerName.length > 0 && (
                        <TouchableOpacity
                          style={styles.clearInputButton}
                          onPress={() => {
                            setCustomerName('');
                            setCustomerPhone('');
                            setShowCustomerSuggestions(false);
                          }}
                        >
                          <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
                        </TouchableOpacity>
                      )}
                    </View>

                    {/* Customer Suggestions */}
                    {showCustomerSuggestions && recentCustomers.length > 0 && (
                      <View style={[styles.suggestionsList, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                        {recentCustomers
                          .filter(c => c.name.toLowerCase().includes(customerName.toLowerCase()))
                          .slice(0, 5)
                          .map((customer, index) => (
                            <TouchableOpacity
                              key={index}
                              style={[styles.suggestionItem, index !== 0 && { borderTopWidth: 1, borderTopColor: theme.border }]}
                              onPress={() => selectCustomer(customer)}
                            >
                              <MaterialIcons name="person" size={20} color={theme.text.secondary} />
                              <View style={styles.suggestionInfo}>
                                <Text style={[styles.suggestionName, { color: theme.text.primary }]}>{customer.name}</Text>
                                {customer.phone !== 'N/A' && (
                                  <Text style={[styles.suggestionPhone, { color: theme.text.secondary }]}>{customer.phone}</Text>
                                )}
                              </View>
                            </TouchableOpacity>
                          ))}
                      </View>
                    )}
                  </View>

                  <View style={styles.inputContainer}>
                    <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Phone Number:</Text>
                    <TextInput
                      style={[styles.input, {
                        backgroundColor: theme.background,
                        borderColor: theme.border,
                        color: theme.text.primary
                      }]}
                      placeholder="Enter phone number (optional)"
                      placeholderTextColor={theme.text.hint}
                      value={customerPhone}
                      onChangeText={setCustomerPhone}
                      keyboardType="phone-pad"
                    />
                  </View>

                  <Text style={[styles.sectionTitle, { color: theme.text.primary, marginTop: 20 }]}>
                    Payment Method
                  </Text>

                  <View style={styles.paymentOptions}>
                    <TouchableOpacity
                      style={[
                        styles.paymentOption,
                        paymentMethod === 'cash' && {
                          backgroundColor: theme.card.statsGreen,
                          borderColor: theme.success
                        },
                        { borderColor: theme.border }
                      ]}
                      onPress={() => setPaymentMethod('cash')}
                    >
                      <MaterialIcons
                        name="payments"
                        size={24}
                        color={paymentMethod === 'cash' ? theme.success : theme.text.secondary}
                      />
                      <Text style={[
                        styles.paymentOptionText,
                        { color: paymentMethod === 'cash' ? theme.success : theme.text.secondary }
                      ]}>
                        Cash
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.paymentOption,
                        paymentMethod === 'card' && {
                          backgroundColor: theme.card.statsBlue,
                          borderColor: theme.secondary
                        },
                        { borderColor: theme.border }
                      ]}
                      onPress={() => setPaymentMethod('card')}
                    >
                      <MaterialIcons
                        name="credit-card"
                        size={24}
                        color={paymentMethod === 'card' ? theme.secondary : theme.text.secondary}
                      />
                      <Text style={[
                        styles.paymentOptionText,
                        { color: paymentMethod === 'card' ? theme.secondary : theme.text.secondary }
                      ]}>
                        Card
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.paymentOption,
                        paymentMethod === 'upi' && {
                          backgroundColor: theme.card.statsYellow,
                          borderColor: theme.accent
                        },
                        { borderColor: theme.border }
                      ]}
                      onPress={() => setPaymentMethod('upi')}
                    >
                      <MaterialIcons
                        name="smartphone"
                        size={24}
                        color={paymentMethod === 'upi' ? theme.accent : theme.text.secondary}
                      />
                      <Text style={[
                        styles.paymentOptionText,
                        { color: paymentMethod === 'upi' ? theme.accent : theme.text.secondary }
                      ]}>
                        UPI
                      </Text>
                    </TouchableOpacity>
                  </View>
                </ScrollView>

                <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                  <TouchableOpacity
                    style={[styles.cancelButton, { borderColor: theme.border }]}
                    onPress={() => setCheckoutModalVisible(false)}
                  >
                    <Text style={[styles.cancelButtonText, { color: theme.text.primary }]}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.confirmButton, { backgroundColor: theme.primary }]}
                    onPress={saveBill}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <ActivityIndicator color="white" size="small" />
                    ) : (
                      <>
                        <MaterialIcons name="receipt" size={20} color="white" />
                        <Text style={styles.confirmButtonText}>Complete Sale</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Bill Preview Modal */}
          <Modal
            visible={billPreviewVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => {
              setBillPreviewVisible(false);
              navigation.navigate('BillingHome');
            }}
          >
            <View style={styles.modalOverlay}>
              <View style={[styles.billPreviewContent, { backgroundColor: theme.surface }]}>
                <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                  <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Bill Preview</Text>
                  <TouchableOpacity
                    onPress={() => {
                      setBillPreviewVisible(false);
                      navigation.navigate('BillingHome');
                    }}
                  >
                    <MaterialIcons name="close" size={24} color={theme.text.primary} />
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.billPreviewScroll}>
                  {currentBill && (
                    <BillTemplate
                      billData={currentBill}
                      shopProfile={shopProfile}
                      theme={theme}
                    />
                  )}
                </ScrollView>

                <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                  <TouchableOpacity
                    style={[styles.shareButton, { backgroundColor: theme.secondary }]}
                    onPress={() => {
                      if (currentBill) {
                        const message = `Bill #${currentBill.id}\n` +
                          `Date: ${new Date(currentBill.createdAt).toLocaleDateString()}\n` +
                          `Customer: ${currentBill.customerName}\n` +
                          `Amount: ₹${currentBill.total.toFixed(2)}\n` +
                          `\nThank you for your business!`;

                        Share.share({
                          message,
                          title: `Bill from ${shopProfile?.shopName || 'Bakery Shop'}`
                        });
                      }
                    }}
                  >
                    <MaterialIcons name="share" size={20} color="white" />
                    <Text style={styles.shareButtonText}>Share Bill</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.printButton, { backgroundColor: theme.primary }]}
                    onPress={async () => {
                      try {
                        // First attempt direct printing
                        const result = await PrintService.directPrint(currentBill, shopProfile);
                        if (!result.success) {
                          // If direct printing fails, show options
                          Alert.alert(
                            'Print Options',
                            'Choose a print option:',
                            [
                              {
                                text: 'Print Preview',
                                onPress: async () => {
                                  const result = await PrintService.showPrintPreview(currentBill, shopProfile);
                                  if (!result.success) {
                                    Alert.alert('Print Error', 'Failed to print bill. Please try again.');
                                  }
                                }
                              },
                              {
                                text: 'Save as PDF',
                                onPress: async () => {
                                  const result = await PrintService.printToPDF(currentBill, shopProfile);
                                  if (result.success) {
                                    Alert.alert('Success', result.message || 'Bill saved as PDF');
                                  } else {
                                    Alert.alert('Error', 'Failed to save bill as PDF. Please try again.');
                                  }
                                }
                              },
                              {
                                text: 'Save to Gallery',
                                onPress: async () => {
                                  const result = await PrintService.saveAsImage(currentBill, shopProfile);
                                  if (result.success) {
                                    Alert.alert('Success', result.message || 'Bill saved to gallery');
                                  } else {
                                    Alert.alert('Error', result.message || 'Failed to save bill to gallery. Please try again.');
                                  }
                                }
                              },
                              { text: 'Cancel', style: 'cancel' }
                            ]
                          );
                        }
                      } catch (error) {
                        console.error('Print error:', error);
                        Alert.alert('Print Error', 'Failed to print bill. Please try again.');
                      }
                    }}
                  >
                    <MaterialIcons name="print" size={20} color="white" />
                    <Text style={styles.printButtonText}>Print Bill</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
  },
  clearButtonText: {
    marginLeft: 4,
    fontWeight: '500',
    fontSize: 14,
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyCartText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  emptyCartSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    marginBottom: 24,
  },
  browseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  browseButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  cartList: {
    paddingBottom: 16,
  },
  cartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  cartItemPrice: {
    fontSize: 14,
    marginTop: 4,
  },
  cartItemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  cartItemActions: {
    flexDirection: 'row',
  },
  cartActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  cartFooter: {
    padding: 16,
    borderTopWidth: 1,
  },
  cartTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cartTotalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cartTotal: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  checkoutButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '90%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
    maxHeight: 500,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  summaryItemInfo: {
    flex: 1,
  },
  summaryItemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryItemPrice: {
    fontSize: 14,
    marginTop: 4,
  },
  summaryItemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  inputContainer: {
    marginBottom: 16,
    position: 'relative',
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  customerInputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    width: '100%',
  },
  clearInputButton: {
    position: 'absolute',
    right: 12,
    padding: 4,
  },
  suggestionsList: {
    position: 'absolute',
    top: 76,
    left: 0,
    right: 0,
    borderWidth: 1,
    borderRadius: 8,
    maxHeight: 200,
    zIndex: 1000,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  suggestionInfo: {
    marginLeft: 8,
    flex: 1,
  },
  suggestionName: {
    fontSize: 14,
    fontWeight: '500',
  },
  suggestionPhone: {
    fontSize: 12,
    marginTop: 2,
  },
  paymentOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  paymentOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  paymentOptionText: {
    marginTop: 8,
    fontWeight: '500',
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    padding: 12,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  billPreviewContent: {
    width: '90%',
    maxHeight: '90%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  billPreviewScroll: {
    maxHeight: 500,
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  shareButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  printButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
  },
  printButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

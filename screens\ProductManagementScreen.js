import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Animated,
  ScrollView,
  Modal,
  SafeAreaView,
  StatusBar,
  Switch,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';
import productService from '../utils/productService';
import apiService from '../utils/apiService';
import firebaseService from '../utils/firebaseService';
import analyticsService from '../utils/analyticsService';
import { useFocusEffect } from '@react-navigation/native';

export default function ProductManagementScreen() {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [productName, setProductName] = useState('');
  const [productPrice, setProductPrice] = useState('');
  const [productCategory, setProductCategory] = useState('own');
  const [isPopular, setIsPopular] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  // Product categories
  const productCategories = [
    { id: 'own', name: 'Own Products' },
    { id: 'biscuits', name: 'Biscuits' },
    { id: 'savouries', name: 'Savouries' },
    { id: 'other', name: 'Other Items' }
  ];

  // Log screen view when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      // Log screen view event
      analyticsService.logScreenView('ProductManagement', 'ProductManagementScreen');
      return () => {};
    }, [])
  );

  useEffect(() => {
    loadProducts();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      filterProducts();
    }
  }, [searchQuery, products]);

  const loadProducts = async () => {
    try {
      setLoading(true);

      // Get products from Firebase
      const firebaseProducts = await firebaseService.getProducts();

      if (firebaseProducts && firebaseProducts.length > 0) {
        console.log('Loaded products from Firebase:', firebaseProducts.length);

        // Format Firebase products for display
        const formattedProducts = firebaseProducts.map(p => ({
          id: p.id || p._id || Date.now().toString(),
          name: p.name,
          price: p.price || 0,
          ingredients: p.ingredients || [],
          category: p.category || 'own',
          image: p.image || null,
          popular: p.popular || false,
          isRecipe: p.category === 'own'
        }));

        // Sort by name
        formattedProducts.sort((a, b) => a.name.localeCompare(b.name));

        setProducts(formattedProducts);
        setFilteredProducts(formattedProducts);
      } else {
        // Fallback to AsyncStorage if Firebase is not available or empty
        console.log('Falling back to AsyncStorage for products');

        // Load recipes (own products)
        const keys = await AsyncStorage.getAllKeys();
        const recipeKeys = keys.filter(k => k.startsWith('recipe_'));
        const recipeData = [];

        for (let key of recipeKeys) {
          const raw = await AsyncStorage.getItem(key);
          const parsed = JSON.parse(raw);
          recipeData.push({
            id: key.replace('recipe_', ''),
            name: key.replace('recipe_', ''),
            price: parsed.sellingPrice ? parseFloat(parsed.sellingPrice) : 0,
            ingredients: parsed.ingredients || [],
            category: parsed.category || 'own',
            image: parsed.image || null,
            popular: parsed.popular || false,
            isRecipe: true
          });
        }

        // Load purchased products
        const purchasedProductsData = await AsyncStorage.getItem('purchased_products');
        const purchasedProducts = purchasedProductsData ? JSON.parse(purchasedProductsData) : [];

        // Combine all products
        const allProducts = [...recipeData, ...purchasedProducts];

        // Sort by name
        allProducts.sort((a, b) => a.name.localeCompare(b.name));

        setProducts(allProducts);
        setFilteredProducts(allProducts);

        // No need to sync with MongoDB anymore
      }
    } catch (error) {
      console.error('Failed to load products:', error);
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const filterProducts = () => {
    if (!searchQuery.trim()) {
      setFilteredProducts(products);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(query) ||
      (productCategories.find(cat => cat.id === product.category)?.name || '').toLowerCase().includes(query)
    );

    setFilteredProducts(filtered);
  };

  const handleAddProduct = () => {
    setProductName('');
    setProductPrice('');
    setProductCategory('biscuits');
    setIsPopular(false);
    setEditMode(false);
    setAddModalVisible(true);

    // Log event
    analyticsService.logCustomEvent('open_add_product_form');
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setProductName(product.name);
    setProductPrice(product.price.toString());
    setProductCategory(product.category || (product.isRecipe ? 'own' : 'other'));
    setIsPopular(product.popular || false);
    setEditMode(true);
    setAddModalVisible(true);

    // Log event
    analyticsService.logCustomEvent('open_edit_product_form', {
      product_id: product.id,
      product_name: product.name,
      product_category: product.category || (product.isRecipe ? 'own' : 'other')
    });
  };

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setModalVisible(true);

    // Log product view event
    analyticsService.logProductView(
      product.id,
      product.name,
      product.category || (product.isRecipe ? 'own' : 'other')
    );
  };

  const saveProduct = async () => {
    if (!productName.trim()) {
      Alert.alert('Error', 'Product name is required');
      return;
    }

    if (!productPrice.trim() || isNaN(parseFloat(productPrice)) || parseFloat(productPrice) <= 0) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }

    try {
      setIsSaving(true);

      // Prepare product data for Firebase
      const productData = {
        name: productName,
        price: parseFloat(productPrice),
        category: productCategory,
        popular: isPopular,
        cost: 0 // Default cost
      };

      // Add ID if editing an existing product
      if (editMode && selectedProduct.id) {
        productData.id = selectedProduct.id;
      }

      // Save to Firebase
      await firebaseService.saveProduct(productData);

      // Also save to AsyncStorage for backward compatibility
      if (editMode) {
        // Editing existing product
        if (selectedProduct.isRecipe) {
          // Update recipe
          const key = `recipe_${selectedProduct.id}`;
          const recipeRaw = await AsyncStorage.getItem(key);
          if (recipeRaw) {
            const recipe = JSON.parse(recipeRaw);
            recipe.sellingPrice = parseFloat(productPrice);
            recipe.category = productCategory;
            recipe.popular = isPopular;
            await AsyncStorage.setItem(key, JSON.stringify(recipe));
          }
        } else {
          // Update purchased product
          const purchasedProductsData = await AsyncStorage.getItem('purchased_products');
          let purchasedProducts = purchasedProductsData ? JSON.parse(purchasedProductsData) : [];

          purchasedProducts = purchasedProducts.map(p =>
            p.id === selectedProduct.id
              ? {
                  ...p,
                  name: productName,
                  price: parseFloat(productPrice),
                  category: productCategory,
                  popular: isPopular
                }
              : p
          );

          await AsyncStorage.setItem('purchased_products', JSON.stringify(purchasedProducts));
        }
      } else {
        // Adding new product (always a purchased product)
        const purchasedProductsData = await AsyncStorage.getItem('purchased_products');
        let purchasedProducts = purchasedProductsData ? JSON.parse(purchasedProductsData) : [];

        // Create new product
        const newProduct = {
          id: Date.now().toString(),
          name: productName,
          price: parseFloat(productPrice),
          category: productCategory,
          popular: isPopular,
          isRecipe: false
        };

        purchasedProducts.push(newProduct);
        await AsyncStorage.setItem('purchased_products', JSON.stringify(purchasedProducts));
      }

      // Reload products
      await loadProducts();

      // Close modal
      setAddModalVisible(false);
      Alert.alert('Success', `Product ${editMode ? 'updated' : 'added'} successfully`);

      // Log product save event
      analyticsService.logCustomEvent(`product_${editMode ? 'updated' : 'created'}`, {
        product_id: editMode && selectedProduct ? selectedProduct.id : 'new',
        product_name: productName,
        product_price: parseFloat(productPrice),
        product_category: productCategory
      });
    } catch (error) {
      console.error('Failed to save product:', error);
      Alert.alert('Error', `Failed to ${editMode ? 'update' : 'add'} product`);
    } finally {
      setIsSaving(false);
    }
  };

  const deleteProduct = async () => {
    if (!selectedProduct) return;

    try {
      if (selectedProduct.isRecipe) {
        Alert.alert(
          'Cannot Delete',
          'Recipe products cannot be deleted from here. Please use the recipe management screen.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Delete from Firebase
      if (selectedProduct.id) {
        await firebaseService.deleteProduct(selectedProduct.id);
      }

      // Also delete from AsyncStorage for backward compatibility
      const purchasedProductsData = await AsyncStorage.getItem('purchased_products');
      let purchasedProducts = purchasedProductsData ? JSON.parse(purchasedProductsData) : [];

      purchasedProducts = purchasedProducts.filter(p => p.id !== selectedProduct.id);
      await AsyncStorage.setItem('purchased_products', JSON.stringify(purchasedProducts));

      // Reload products
      await loadProducts();

      // Close modal
      setModalVisible(false);
      setAddModalVisible(false);
      Alert.alert('Success', 'Product deleted successfully');

      // Log product delete event
      analyticsService.logCustomEvent('product_deleted', {
        product_id: selectedProduct.id,
        product_name: selectedProduct.name,
        product_category: selectedProduct.category || (selectedProduct.isRecipe ? 'own' : 'other')
      });
    } catch (error) {
      console.error('Failed to delete product:', error);
      Alert.alert('Error', 'Failed to delete product');
    }
  };

  const getCategoryName = (categoryId) => {
    const category = productCategories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Uncategorized';
  };

  const renderProductItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.productCard, { backgroundColor: theme.surface }]}
      onPress={() => handleViewProduct(item)}
      activeOpacity={0.7}
    >
      <View style={styles.productHeader}>
        <Text style={[styles.productName, { color: theme.text.primary }]}>{item.name}</Text>
        {item.popular && (
          <View style={[styles.popularBadge, { backgroundColor: theme.accent }]}>
            <Text style={styles.popularBadgeText}>Popular</Text>
          </View>
        )}
      </View>

      <View style={styles.productDetails}>
        <View style={styles.productInfo}>
          <Text style={[styles.productCategory, { color: theme.text.secondary }]}>
            {getCategoryName(item.category)}
          </Text>
          <Text style={[styles.productPrice, { color: theme.success }]}>₹{item.price.toFixed(2)}</Text>
        </View>

        <TouchableOpacity
          style={[styles.editButton, { backgroundColor: theme.primary }]}
          onPress={() => handleEditProduct(item)}
        >
          <MaterialIcons name="edit" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.text.primary }]}>Product Management</Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.success }]}
              onPress={handleAddProduct}
            >
              <MaterialIcons name="add" size={24} color="white" />
              <Text style={styles.addButtonText}>Add Product</Text>
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View style={[styles.searchBar, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <MaterialIcons name="search" size={20} color={theme.text.secondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.text.primary }]}
              placeholder="Search products..."
              placeholderTextColor={theme.text.hint}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
              </TouchableOpacity>
            ) : null}
          </View>

          {/* Products List */}
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.primary} />
              <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading products...</Text>
            </View>
          ) : filteredProducts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="inventory" size={64} color={theme.text.disabled} />
              <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
                {searchQuery ? 'No products found matching your search' : 'No products available'}
              </Text>
              <TouchableOpacity
                style={[styles.emptyAddButton, { backgroundColor: theme.primary }]}
                onPress={handleAddProduct}
              >
                <Text style={styles.emptyAddButtonText}>Add Your First Product</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={filteredProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.productsList}
              numColumns={2}
            />
          )}

          {/* View Product Modal */}
          <Modal
            visible={modalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setModalVisible(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                {selectedProduct && (
                  <>
                    <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                      <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Product Details</Text>
                      <TouchableOpacity onPress={() => setModalVisible(false)}>
                        <MaterialIcons name="close" size={24} color={theme.text.primary} />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Name:</Text>
                      <Text style={[styles.detailValue, { color: theme.text.primary }]}>{selectedProduct.name}</Text>

                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Price:</Text>
                      <Text style={[styles.detailValue, { color: theme.success }]}>₹{selectedProduct.price.toFixed(2)}</Text>

                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Category:</Text>
                      <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                        {getCategoryName(selectedProduct.category)}
                      </Text>

                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Type:</Text>
                      <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                        {selectedProduct.isRecipe ? 'Own Product (Recipe)' : 'Purchased Product'}
                      </Text>

                      <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Popular:</Text>
                      <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                        {selectedProduct.popular ? 'Yes' : 'No'}
                      </Text>
                    </View>

                    <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                      {!selectedProduct.isRecipe && (
                        <TouchableOpacity
                          style={[styles.deleteButton, { backgroundColor: theme.error }]}
                          onPress={() => {
                            Alert.alert(
                              'Confirm Delete',
                              'Are you sure you want to delete this product?',
                              [
                                { text: 'Cancel', style: 'cancel' },
                                { text: 'Delete', onPress: deleteProduct, style: 'destructive' }
                              ]
                            );
                          }}
                        >
                          <MaterialIcons name="delete" size={20} color="white" />
                          <Text style={styles.deleteButtonText}>Delete</Text>
                        </TouchableOpacity>
                      )}
                      <TouchableOpacity
                        style={[styles.editModalButton, { backgroundColor: theme.primary }]}
                        onPress={() => {
                          setModalVisible(false);
                          handleEditProduct(selectedProduct);
                        }}
                      >
                        <MaterialIcons name="edit" size={20} color="white" />
                        <Text style={styles.editButtonText}>Edit</Text>
                      </TouchableOpacity>
                    </View>
                  </>
                )}
              </View>
            </View>
          </Modal>

          {/* Add/Edit Product Modal */}
          <Modal
            visible={addModalVisible}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setAddModalVisible(false)}
          >
            <View style={styles.modalOverlay}>
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{ width: '100%' }}
              >
                <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                  <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                    <Text style={[styles.modalTitle, { color: theme.text.primary }]}>
                      {editMode ? 'Edit Product' : 'Add New Product'}
                    </Text>
                    <TouchableOpacity onPress={() => setAddModalVisible(false)}>
                      <MaterialIcons name="close" size={24} color={theme.text.primary} />
                    </TouchableOpacity>
                  </View>

                  <ScrollView style={styles.modalBody}>
                    <View style={styles.inputContainer}>
                      <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Product Name:</Text>
                      <TextInput
                        style={[styles.input, {
                          backgroundColor: theme.background,
                          borderColor: theme.border,
                          color: theme.text.primary
                        }]}
                        placeholder="Enter product name"
                        placeholderTextColor={theme.text.hint}
                        value={productName}
                        onChangeText={setProductName}
                        editable={!selectedProduct?.isRecipe}
                      />
                      {selectedProduct?.isRecipe && (
                        <Text style={[styles.helperText, { color: theme.text.hint }]}>
                          Recipe names cannot be changed here. Use recipe management instead.
                        </Text>
                      )}
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Price (₹):</Text>
                      <TextInput
                        style={[styles.input, {
                          backgroundColor: theme.background,
                          borderColor: theme.border,
                          color: theme.text.primary
                        }]}
                        placeholder="Enter price"
                        placeholderTextColor={theme.text.hint}
                        value={productPrice}
                        onChangeText={setProductPrice}
                        keyboardType="numeric"
                      />
                    </View>

                    <View style={styles.inputContainer}>
                      <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Category:</Text>
                      <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={styles.categoriesContainer}
                      >
                        {productCategories.map(category => (
                          <TouchableOpacity
                            key={category.id}
                            style={[
                              styles.categoryButton,
                              productCategory === category.id && { backgroundColor: theme.primary },
                              { borderColor: theme.border }
                            ]}
                            onPress={() => setProductCategory(category.id)}
                            disabled={selectedProduct?.isRecipe && category.id !== 'own'}
                          >
                            <Text
                              style={[
                                styles.categoryButtonText,
                                { color: productCategory === category.id ? 'white' : theme.text.primary }
                              ]}
                            >
                              {category.name}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                      {selectedProduct?.isRecipe && (
                        <Text style={[styles.helperText, { color: theme.text.hint }]}>
                          Recipe products must be in the "Own Products" category.
                        </Text>
                      )}
                    </View>

                    <View style={styles.switchContainer}>
                      <Text style={[styles.switchLabel, { color: theme.text.secondary }]}>Mark as Popular:</Text>
                      <Switch
                        value={isPopular}
                        onValueChange={setIsPopular}
                        trackColor={{ false: theme.border, true: theme.primary }}
                        thumbColor={isPopular ? theme.accent : '#f4f3f4'}
                      />
                    </View>
                  </ScrollView>

                  <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                    <TouchableOpacity
                      style={[styles.cancelButton, { borderColor: theme.border }]}
                      onPress={() => setAddModalVisible(false)}
                    >
                      <Text style={[styles.cancelButtonText, { color: theme.text.primary }]}>Cancel</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.saveButton, { backgroundColor: theme.success }]}
                      onPress={saveProduct}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <ActivityIndicator color="white" size="small" />
                      ) : (
                        <>
                          <MaterialIcons name="save" size={20} color="white" />
                          <Text style={styles.saveButtonText}>Save</Text>
                        </>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </KeyboardAvoidingView>
            </View>
          </Modal>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    height: 44,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  productsList: {
    paddingBottom: 16,
  },
  productCard: {
    flex: 1,
    margin: 6,
    padding: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  popularBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 4,
  },
  popularBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  productDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  productInfo: {
    flex: 1,
  },
  productCategory: {
    fontSize: 12,
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    width: '100%',
    maxWidth: 500,
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  detailLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    marginBottom: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  deleteButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  editModalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
  },
  editButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  categoriesContainer: {
    marginBottom: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    padding: 12,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

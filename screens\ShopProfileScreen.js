import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Animated,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function ShopProfileScreen() {
  const [shopName, setShopName] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [gstNumber, setGstNumber] = useState('');
  const [quotes, setQuotes] = useState(['Thank you for your business!']);
  const [newQuote, setNewQuote] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  useEffect(() => {
    loadShopProfile();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadShopProfile = async () => {
    try {
      const shopProfileData = await AsyncStorage.getItem('shop_profile');
      if (shopProfileData) {
        const profile = JSON.parse(shopProfileData);
        setShopName(profile.shopName || '');
        setAddress(profile.address || '');
        setPhone(profile.phone || '');
        setEmail(profile.email || '');
        setGstNumber(profile.gstNumber || '');
        setQuotes(profile.quotes || ['Thank you for your business!']);
      }
    } catch (error) {
      console.error('Failed to load shop profile:', error);
      Alert.alert('Error', 'Failed to load shop profile');
    }
  };

  const saveShopProfile = async () => {
    if (!shopName.trim()) {
      Alert.alert('Error', 'Shop name is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const shopProfile = {
        shopName,
        address,
        phone,
        email,
        gstNumber,
        quotes
      };
      
      await AsyncStorage.setItem('shop_profile', JSON.stringify(shopProfile));
      
      Alert.alert('Success', 'Shop profile saved successfully');
    } catch (error) {
      console.error('Failed to save shop profile:', error);
      Alert.alert('Error', 'Failed to save shop profile');
    } finally {
      setIsSaving(false);
    }
  };

  const addQuote = () => {
    if (!newQuote.trim()) return;
    
    setQuotes([...quotes, newQuote.trim()]);
    setNewQuote('');
  };

  const removeQuote = (index) => {
    const updatedQuotes = [...quotes];
    updatedQuotes.splice(index, 1);
    setQuotes(updatedQuotes);
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <KeyboardAvoidingView 
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <View style={[styles.container, { backgroundColor: theme.background }]}>
          <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
            <Text style={[styles.title, { color: theme.text.primary }]}>Shop Profile</Text>
            <Text style={[styles.subtitle, { color: theme.text.secondary }]}>
              This information will appear on your bills and receipts
            </Text>

            <ScrollView style={styles.formContainer}>
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Business Information</Text>
                
                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Shop Name *</Text>
                  <TextInput
                    style={[styles.input, { 
                      backgroundColor: theme.surface, 
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Enter shop name"
                    placeholderTextColor={theme.text.hint}
                    value={shopName}
                    onChangeText={setShopName}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Address</Text>
                  <TextInput
                    style={[styles.input, { 
                      backgroundColor: theme.surface, 
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Enter address"
                    placeholderTextColor={theme.text.hint}
                    value={address}
                    onChangeText={setAddress}
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Phone Number</Text>
                  <TextInput
                    style={[styles.input, { 
                      backgroundColor: theme.surface, 
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Enter phone number"
                    placeholderTextColor={theme.text.hint}
                    value={phone}
                    onChangeText={setPhone}
                    keyboardType="phone-pad"
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>Email</Text>
                  <TextInput
                    style={[styles.input, { 
                      backgroundColor: theme.surface, 
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Enter email address"
                    placeholderTextColor={theme.text.hint}
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Text style={[styles.inputLabel, { color: theme.text.secondary }]}>GST Number</Text>
                  <TextInput
                    style={[styles.input, { 
                      backgroundColor: theme.surface, 
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Enter GST number (if applicable)"
                    placeholderTextColor={theme.text.hint}
                    value={gstNumber}
                    onChangeText={setGstNumber}
                    autoCapitalize="characters"
                  />
                </View>
              </View>

              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Bill Quotes</Text>
                <Text style={[styles.sectionDescription, { color: theme.text.secondary }]}>
                  Add quotes or messages to display at the bottom of your bills
                </Text>

                {quotes.map((quote, index) => (
                  <View key={index} style={[styles.quoteItem, { backgroundColor: theme.surface }]}>
                    <Text style={[styles.quoteText, { color: theme.text.primary }]}>{quote}</Text>
                    <TouchableOpacity 
                      style={styles.removeQuoteButton}
                      onPress={() => removeQuote(index)}
                    >
                      <MaterialIcons name="delete" size={20} color={theme.error} />
                    </TouchableOpacity>
                  </View>
                ))}

                <View style={styles.addQuoteContainer}>
                  <TextInput
                    style={[styles.quoteInput, { 
                      backgroundColor: theme.surface, 
                      borderColor: theme.border,
                      color: theme.text.primary
                    }]}
                    placeholder="Add a new quote or message"
                    placeholderTextColor={theme.text.hint}
                    value={newQuote}
                    onChangeText={setNewQuote}
                  />
                  <TouchableOpacity 
                    style={[styles.addQuoteButton, { backgroundColor: theme.primary }]}
                    onPress={addQuote}
                  >
                    <MaterialIcons name="add" size={24} color="white" />
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>

            <View style={styles.footer}>
              <TouchableOpacity
                style={[styles.saveButton, { backgroundColor: theme.success }]}
                onPress={saveShopProfile}
                disabled={isSaving}
              >
                {isSaving ? (
                  <ActivityIndicator color="white" size="small" />
                ) : (
                  <>
                    <MaterialIcons name="save" size={20} color="white" />
                    <Text style={styles.saveButtonText}>Save Profile</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
  },
  formContainer: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  quoteItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  quoteText: {
    flex: 1,
    fontSize: 14,
  },
  removeQuoteButton: {
    padding: 4,
  },
  addQuoteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  quoteInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginRight: 8,
  },
  addQuoteButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    marginTop: 16,
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

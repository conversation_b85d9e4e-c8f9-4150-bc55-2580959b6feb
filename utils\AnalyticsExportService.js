import * as Print from 'expo-print';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

class AnalyticsExportService {
  static async exportAnalyticsToPDF(analyticsData, shopProfile, timeRange) {
    try {
      const html = this.generateAnalyticsHTML(analyticsData, shopProfile, timeRange);

      const { uri } = await Print.printToFileAsync({ html });
      const fileName = `Analytics_${timeRange.replace(/\W/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.copyAsync({ from: uri, to: fileUri });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Export Analytics Report'
        });
        return { success: true, uri: fileUri };
      } else {
        return { success: false, message: 'Sharing not available' };
      }
    } catch (error) {
      console.error('Export error:', error);
      return { success: false, error, message: 'Export failed' };
    }
  }

  static generateAnalyticsHTML(data, shop, range) {
    return `
      <html>
        <body style="font-family: sans-serif; padding: 24px;">
          <h1>${shop?.shopName || 'Bakery'} - Analytics Report</h1>
          <h3>Time Range: ${range}</h3>
          <p>Date: ${new Date().toLocaleDateString()}</p>
          <table border="1" cellspacing="0" cellpadding="8">
            <tr><th>Total Products</th><th>Revenue</th><th>Cost</th><th>Profit</th></tr>
            <tr>
              <td>${data.totalProducts}</td>
              <td>₹${data.totalRevenue.toFixed(2)}</td>
              <td>₹${data.totalCost.toFixed(2)}</td>
              <td>₹${data.totalProfit.toFixed(2)}</td>
            </tr>
          </table>
        </body>
      </html>`;
  }
}

export default AnalyticsExportService;

import React, { useEffect, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function BillingHomeScreen() {
  const navigation = useNavigation();
  const { theme, userRole, logout } = useContext(AppContext);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleLogout = async () => {
    await logout();
    navigation.replace('Login');
  };

  const menuItems = [
    {
      title: 'New Sale',
      icon: 'add-shopping-cart',
      color: theme.success,
      screen: 'ProductSelection',
      description: 'Create a new sale by selecting products'
    },
    {
      title: 'View Cart',
      icon: 'shopping-cart',
      color: theme.primary,
      screen: 'Cart',
      description: 'View and manage current shopping cart'
    },
    {
      title: 'Sales History',
      icon: 'history',
      color: theme.secondary,
      screen: 'BillingHistory',
      description: 'View past sales and transactions'
    },
    // Admin-only options
    ...(userRole === 'admin' ? [
      {
        title: 'Manage Products',
        icon: 'inventory',
        color: theme.accent,
        screen: 'ProductManagement',
        description: 'Add and manage products for sale'
      },
      {
        title: 'Shop Profile',
        icon: 'store',
        color: theme.secondary,
        screen: 'ShopProfile',
        description: 'Manage shop details and bill information'
      }
    ] : [])
  ];

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.text.primary }]}>Billing System</Text>
            {userRole === 'staff' && (
              <TouchableOpacity
                style={[styles.logoutButton, { backgroundColor: theme.error }]}
                onPress={handleLogout}
              >
                <MaterialIcons name="logout" size={20} color="#fff" />
                <Text style={styles.logoutButtonText}>Logout</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Menu Cards */}
          <ScrollView style={styles.menuContainer}>
            {menuItems.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.menuCard, { backgroundColor: theme.surface }]}
                onPress={() => navigation.navigate(item.screen)}
              >
                <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
                  <MaterialIcons name={item.icon} size={32} color="white" />
                </View>
                <View style={styles.menuTextContainer}>
                  <Text style={[styles.menuTitle, { color: theme.text.primary }]}>{item.title}</Text>
                  <Text style={[styles.menuDescription, { color: theme.text.secondary }]}>
                    {item.description}
                  </Text>
                </View>
                <MaterialIcons name="chevron-right" size={24} color={theme.text.secondary} />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  logoutButtonText: {
    color: 'white',
    marginLeft: 6,
    fontWeight: '500',
    fontSize: 14,
  },
  menuContainer: {
    flex: 1,
  },
  menuCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  menuDescription: {
    fontSize: 14,
  },
});

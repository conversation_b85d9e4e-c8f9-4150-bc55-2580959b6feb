import AsyncStorage from '@react-native-async-storage/async-storage';

// Function to save a product to AsyncStorage
export const saveProduct = async (product) => {
  try {
    // Get existing products
    const productsData = await AsyncStorage.getItem('products');
    let products = productsData ? JSON.parse(productsData) : [];

    if (product.id) {
      // Update existing product
      products = products.map(p => p.id === product.id ? { ...product } : p);
    } else {
      // Create new product with unique ID
      product.id = Date.now().toString();
      products.push(product);
    }

    // Save updated products list
    await AsyncStorage.setItem('products', JSON.stringify(products));

    console.log('Product saved successfully');
    return { success: true, product };
  } catch (error) {
    console.error('Error saving product:', error);
    return { success: false, error: error.message };
  }
};

// Function to get all products
export const getProducts = async () => {
  try {
    // Get products from AsyncStorage
    const productsData = await AsyncStorage.getItem('products');
    const products = productsData ? JSON.parse(productsData) : [];

    return products.filter(p => p.isActive !== false);
  } catch (error) {
    console.error('Error getting products:', error);
    return [];
  }
};

// Function to delete a product
export const deleteProduct = async (productId) => {
  try {
    // Get existing products
    const productsData = await AsyncStorage.getItem('products');
    let products = productsData ? JSON.parse(productsData) : [];

    // Soft delete by setting isActive to false
    products = products.map(p => p.id === productId ? { ...p, isActive: false } : p);

    // Save updated products list
    await AsyncStorage.setItem('products', JSON.stringify(products));

    console.log('Product deleted successfully');
    return { success: true };
  } catch (error) {
    console.error('Error deleting product:', error);
    return { success: false, error: error.message };
  }
};

// Export default object with all functions
export default {
  saveProduct,
  getProducts,
  deleteProduct
};

import React, { useEffect, useState, useContext } from 'react';
import { View, Text, FlatList, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import * as Print from 'expo-print';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppContext } from '../AppContext';
import { MaterialIcons } from '@expo/vector-icons';

export default function ProductionHistoryScreen() {
  const [entries, setEntries] = useState([]);
  const { theme } = useContext(AppContext);

  useEffect(() => {
    loadProductionData();
  }, []);

  const loadProductionData = async () => {
    try {
      const raw = await AsyncStorage.getItem('production_data');
      const parsed = JSON.parse(raw) || [];
      setEntries(parsed.reverse());
    } catch (e) {
      Alert.alert('Error', 'Failed to load production history.');
    }
  };

  const renderItem = ({ item }) => (
    <View style={[styles.item, { backgroundColor: theme.surface, borderColor: theme.border }]}>
      <Text style={[styles.line, { color: theme.text.secondary }]}>{item.date}</Text>
      <Text style={[styles.line, { color: theme.text.primary }]}>🧁 {item.product}</Text>
      <Text style={[styles.line, { color: theme.text.secondary }]}>Qty: {item.qty}</Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Text style={[styles.title, { color: theme.text.primary }]}>Production History</Text>
      <TouchableOpacity
        style={styles.exportButton}
        onPress={async () => {
        try {
          const content = entries.map(e => `${e.date} - ${e.product} - Qty: ${e.qty}`).join('<br>');
          const html = `<pre>${content}</pre>`;
          await Print.printAsync({ html });
        } catch (e) {
          Alert.alert('Export Failed', 'Could not generate PDF.');
        }
      }}>
        <View style={styles.exportButtonContent}>
          <MaterialIcons name="picture-as-pdf" size={18} color="white" />
          <Text style={styles.exportButtonText}>Export to PDF</Text>
        </View>
      </TouchableOpacity>
      <FlatList
        data={entries}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        ListEmptyComponent={<Text style={{ textAlign: 'center', marginTop: 20, color: theme.text.secondary }}>No production records yet.</Text>}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 16 },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 16, textAlign: 'center' },
  item: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
  },
  line: { fontSize: 16 },
  exportButton: {
    backgroundColor: '#9C27B0',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
    elevation: 2,
  },
  exportButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  exportButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

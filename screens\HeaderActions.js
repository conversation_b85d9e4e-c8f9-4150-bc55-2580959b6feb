import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { AppContext } from '../AppContext';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

export default function HeaderActions() {
  const { toggleTheme, logout, isDarkMode, theme } = useContext(AppContext);
  const navigation = useNavigation();

  const handleLogout = async () => {
    await logout();
    navigation.replace('Login');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.surface }]}>
      <View style={styles.buttonGroup}>
        <TouchableOpacity
          style={[styles.themeToggle, { backgroundColor: isDarkMode ? theme.card.statsBlue : theme.card.statsYellow }]}
          onPress={toggleTheme}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name={isDarkMode ? "light-mode" : "dark-mode"}
            size={20}
            color={isDarkMode ? "#fff" : "#333"}
          />
          <Text style={[styles.buttonText, { color: isDarkMode ? "#fff" : "#333" }]}>
            {isDarkMode ? "Light" : "Dark"} Mode
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.dbButton, { backgroundColor: theme.accent }]}
          onPress={() => navigation.navigate('Dashboard', { screen: 'MongoDBSetup' })}
          activeOpacity={0.7}
        >
          <MaterialIcons name="storage" size={20} color="#fff" />
          <Text style={[styles.buttonText, { color: "#fff" }]}>MongoDB</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={[styles.logoutButton, { backgroundColor: theme.error }]}
        onPress={handleLogout}
        activeOpacity={0.7}
      >
        <MaterialIcons name="logout" size={20} color="#fff" />
        <Text style={[styles.buttonText, { color: "#fff" }]}>Logout</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  buttonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  themeToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
  },
  dbButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  buttonText: {
    marginLeft: 6,
    fontWeight: '500',
    fontSize: 14,
  },
});

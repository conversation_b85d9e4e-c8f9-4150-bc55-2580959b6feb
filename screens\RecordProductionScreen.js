import React, { useEffect, useState, useRef, useContext } from 'react';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Alert,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  ScrollView
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Picker as RNPickerSelect } from '@react-native-picker/picker';
import { MaterialIcons } from '@expo/vector-icons';
import { AppContext } from '../AppContext';

export default function RecordProductionScreen() {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [quantity, setQuantity] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const navigation = useNavigation();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const { theme } = useContext(AppContext);

  // Refresh products when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadProducts();

      // Start fade-in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();

      return () => {};
    }, [])
  );


  useEffect(() => {
    loadProducts();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadProducts = async () => {
    setIsRefreshing(true);
    try {
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));
      const names = recipeKeys.map(k => k.replace('recipe_', ''));
      setProducts(names);
    } catch (error) {
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setIsRefreshing(false);
    }
  };

  const saveProduction = async () => {
    if (!selectedProduct || !quantity) {
      Alert.alert('Missing Info', 'Please select a product and enter quantity');
      return;
    }

    setIsLoading(true);

    const today = new Date().toISOString().split('T')[0];
    const entry = { product: selectedProduct, qty: parseFloat(quantity), date: today };
    try {
      const existing = JSON.parse(await AsyncStorage.getItem('production_data') || '[]');
      existing.push(entry);
      await AsyncStorage.setItem('production_data', JSON.stringify(existing));

      const key = `recipe_${selectedProduct}`;
      const recipe = JSON.parse(await AsyncStorage.getItem(key));
      if (recipe) {
        recipe.ingredients.forEach(i => {
          const perUnit = i.amount / recipe.servings;
          i.stock = (i.stock || 0) - perUnit * parseFloat(quantity);
        });
        await AsyncStorage.setItem(key, JSON.stringify(recipe));
      }

      // Success animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      Alert.alert('Success', 'Production recorded and stock updated');
      setQuantity('');
    } catch (e) {
      Alert.alert('Error', 'Failed to save production');
    } finally {
      setIsLoading(false);
    }
  };

  const editRecipe = (productName) => {
    navigation.navigate('Add Recipe', { editMode: true, productName });
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      <Animated.View style={{ opacity: fadeAnim }}>
        <Text style={[styles.title, { color: theme.text.primary }]}>Record Production</Text>

        {isRefreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.secondary} />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading products...</Text>
          </View>
        ) : products.length === 0 ? (
          <View style={[styles.emptyContainer, { backgroundColor: theme.surface }]}>
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No products found</Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.secondary }]}
              onPress={() => navigation.navigate('Add Recipe')}
            >
              <Text style={styles.addButtonText}>Add Your First Recipe</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {Platform.OS === 'android' ? (
              <View style={[styles.pickerContainer, { borderColor: theme.border, backgroundColor: theme.surface }]}>
                <RNPickerSelect
                  selectedValue={selectedProduct}
                  onValueChange={setSelectedProduct}
                  style={{ inputAndroid: { ...styles.picker, color: theme.text.primary } }}
                >
                  <RNPickerSelect.Item label="Select Product" value="" />
                  {products.map(p => (
                    <RNPickerSelect.Item label={p} value={p} key={p} />
                  ))}
                </RNPickerSelect>
              </View>
            ) : (
              <TextInput
                placeholder="Enter product name"
                placeholderTextColor={theme.text.hint}
                value={selectedProduct}
                onChangeText={setSelectedProduct}
                style={[styles.input, { borderColor: theme.border, backgroundColor: theme.surface, color: theme.text.primary }]}
              />
            )}

            <View style={styles.recipeList}>
              <Text style={[styles.subtitle, { color: theme.text.primary }]}>Available Recipes:</Text>
              {products.map(product => (
                <View key={product} style={[styles.recipeItem, { backgroundColor: theme.card.background }]}>
                  <Text style={[styles.recipeName, { color: theme.text.primary }]}>{product}</Text>
                  <View style={styles.recipeActions}>
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: theme.success }]}
                      onPress={() => setSelectedProduct(product)}
                    >
                      <MaterialIcons name="check" size={18} color="white" />
                      <Text style={styles.actionButtonText}>Select</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: theme.accent }]}
                      onPress={() => editRecipe(product)}
                    >
                      <MaterialIcons name="edit" size={18} color="white" />
                      <Text style={styles.actionButtonText}>Edit</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>

            <TextInput
              style={[styles.input, { borderColor: theme.border, backgroundColor: theme.surface, color: theme.text.primary }]}
              placeholderTextColor={theme.text.hint}
              placeholder="Quantity Produced"
              keyboardType="numeric"
              value={quantity}
              onChangeText={setQuantity}
            />

            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: theme.primary }, isLoading && { backgroundColor: theme.text.disabled }]}
              onPress={saveProduction}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <Text style={styles.saveButtonText}>Save Production</Text>
              )}
            </TouchableOpacity>
          </>
        )}

        <View style={styles.navigationButtons}>
          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: theme.secondary }]}
            onPress={() => navigation.navigate('Add Recipe')}
          >
            <Text style={styles.navButtonText}>Add New Recipe</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, { backgroundColor: theme.accent }]}
            onPress={() => navigation.navigate('Dashboard')}
          >
            <Text style={styles.navButtonText}>Go to Dashboard</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
    textAlign: 'center'
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 10,
    color: '#555'
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
    fontSize: 16,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
    overflow: 'hidden'
  },
  picker: {
    height: 50,
    width: '100%',
    color: '#333',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  addButton: {
    backgroundColor: '#007BFF',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  recipeList: {
    marginBottom: 20,
  },
  recipeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  recipeName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  recipeActions: {
    flexDirection: 'row',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginLeft: 8,
  },
  selectButton: {
    backgroundColor: '#4CAF50',
  },
  editButton: {
    backgroundColor: '#FF9800',
  },
  actionButtonText: {
    color: 'white',
    marginLeft: 4,
    fontSize: 14,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  saveButtonDisabled: {
    backgroundColor: '#a5d6a7',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  navButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  navButtonText: {
    color: 'white',
    fontWeight: '500',
  },
});

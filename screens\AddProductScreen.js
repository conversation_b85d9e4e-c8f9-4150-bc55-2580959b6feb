import React, { useState, useCallback, useRef, useEffect, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  ActivityIndicator,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

const IngredientRow = React.memo(({ item, index, onChange, onDelete, theme }) => {
  return (
    <Animated.View style={[styles.ingredientRow, { opacity: 1, backgroundColor: theme.surface, borderColor: theme.border }]}>
      <View style={styles.rowTop}>
        <TextInput
          style={[styles.inputFlex, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
          placeholderTextColor={theme.text.hint}
          placeholder="Name"
          value={item.name}
          onChangeText={(text) => onChange(index, 'name', text)}
          returnKeyType="next"
        />
        <TextInput
          style={[styles.inputFlex, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
          placeholderTextColor={theme.text.hint}
          placeholder="Amount"
          value={item.amount}
          onChangeText={(text) => onChange(index, 'amount', text)}
          keyboardType="numeric"
          returnKeyType="next"
        />
      </View>
      <View style={styles.rowBottom}>
        <TextInput
          style={[styles.inputFlex, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
          placeholderTextColor={theme.text.hint}
          placeholder="Unit"
          value={item.unit}
          onChangeText={(text) => onChange(index, 'unit', text)}
          returnKeyType="next"
        />
        <TextInput
          style={[styles.inputFlex, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
          placeholderTextColor={theme.text.hint}
          placeholder="Price (₹)"
          value={item.price}
          onChangeText={(text) => onChange(index, 'price', text)}
          keyboardType="numeric"
          returnKeyType="done"
        />
      </View>
      {index > 0 && (
        <TouchableOpacity style={[styles.deleteButton, { backgroundColor: theme.error }]} onPress={() => onDelete(index)}>
          <Text style={styles.deleteButtonText}>×</Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
});

export default function AddProductScreen({ route }) {
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);
  const [productName, setProductName] = useState('');
  const [originalName, setOriginalName] = useState('');
  const [servings, setServings] = useState('');
  const [ingredients, setIngredients] = useState([
    { name: '', amount: '', unit: 'g', price: '' },
  ]);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // References for TextInputs
  const servingsInputRef = useRef(null);
  const ingredientRefs = useRef([]);

  useEffect(() => {
    // Check if we're in edit mode
    if (route.params?.editMode && route.params?.productName) {
      setIsEditMode(true);
      loadRecipeForEditing(route.params.productName);
    }

    // Start fade-in and slide-up animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, [route.params]);

  const loadRecipeForEditing = async (name) => {
    try {
      const recipeKey = `recipe_${name}`;
      const recipeData = await AsyncStorage.getItem(recipeKey);

      if (recipeData) {
        const recipe = JSON.parse(recipeData);
        setProductName(name);
        setOriginalName(name);
        setServings(recipe.servings.toString());
        setIngredients(recipe.ingredients || []);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load recipe for editing');
    }
  };

  const updateIngredient = useCallback((index, field, value) => {
    setIngredients((prev) => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  }, []);

  const addIngredientRow = () => {
    setIngredients((prev) => [...prev, { name: '', amount: '', unit: 'g', price: '' }]);
    // Focus on the new row after it's added
    setTimeout(() => {
      if (ingredientRefs.current[ingredients.length]) {
        ingredientRefs.current[ingredients.length].focus();
      }
    }, 100);
  };

  const deleteIngredientRow = (index) => {
    setIngredients((prev) => prev.filter((_, i) => i !== index));
  };

  const saveRecipe = async () => {
    // Dismiss keyboard
    Keyboard.dismiss();

    if (!productName || !servings || ingredients.some(i => !i.name || !i.amount)) {
      Alert.alert('Error', 'Please complete all fields');
      return;
    }

    setIsSaving(true);

    const servingsNum = parseFloat(servings);
    const perUnit = ingredients.map(i => ({
      name: i.name,
      amount: parseFloat(i.amount) / servingsNum,
      unit: i.unit,
      stock: i.stock || parseFloat(i.amount) * 5, // Keep existing stock or set initial stock
      min: i.min || parseFloat(i.amount), // Keep existing min or set default
      price: i.price || '0', // Keep existing price or set default
    }));
    const recipe = { servings: servingsNum, ingredients, perUnit };

    try {
      // If we're in edit mode and the name has changed, delete the old recipe
      if (isEditMode && originalName !== productName) {
        await AsyncStorage.removeItem(`recipe_${originalName}`);
      }

      // Save the recipe with the current name
      await AsyncStorage.setItem(`recipe_${productName}`, JSON.stringify(recipe));

      // Success animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      Alert.alert(
        'Success',
        isEditMode ? 'Recipe updated!' : 'Recipe saved!',
        [{
          text: 'OK',
          onPress: () => {
            if (isEditMode) {
              navigation.goBack();
            } else {
              // Reset form for new recipe
              setProductName('');
              setServings('');
              setIngredients([{ name: '', amount: '', unit: 'g', price: '' }]);
            }
          }
        }]
      );
    } catch (e) {
      Alert.alert('Error', isEditMode ? 'Failed to update recipe' : 'Failed to save recipe');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        style={[styles.container, { backgroundColor: theme.background }]}
        contentContainerStyle={{ paddingBottom: 40 }}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View style={{
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }}>
          <Text style={[styles.title, { color: theme.text.primary }]}>{isEditMode ? 'Edit Recipe' : 'Add Product Recipe'}</Text>

          <TextInput
            style={[styles.input, { backgroundColor: theme.surface, borderColor: theme.border, color: theme.text.primary }]}
            placeholderTextColor={theme.text.hint}
            placeholder="Product Name"
            value={productName}
            onChangeText={setProductName}
            returnKeyType="next"
            onSubmitEditing={() => servingsInputRef.current?.focus()}
          />

          <TextInput
            ref={servingsInputRef}
            style={[styles.input, { backgroundColor: theme.surface, borderColor: theme.border, color: theme.text.primary }]}
            placeholderTextColor={theme.text.hint}
            placeholder="Servings (how many units)"
            value={servings}
            onChangeText={setServings}
            keyboardType="numeric"
            returnKeyType="next"
          />

          <Text style={[styles.subtitle, { color: theme.text.primary }]}>Ingredients:</Text>

          {ingredients.map((item, index) => (
            <IngredientRow
              key={index.toString()}
              item={item}
              index={index}
              onChange={updateIngredient}
              onDelete={deleteIngredientRow}
              theme={theme}
            />
          ))}

          <TouchableOpacity
            style={[styles.addIngredientButton, { backgroundColor: theme.card.statsBlue }]}
            onPress={addIngredientRow}
            activeOpacity={0.7}
          >
            <Text style={[styles.addMore, { color: theme.primary }]}>+ Add Ingredient</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.primary }, isSaving && { backgroundColor: theme.text.disabled }]}
            onPress={saveRecipe}
            disabled={isSaving}
            activeOpacity={0.8}
          >
            {isSaving ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.saveButtonText}>{isEditMode ? 'Update Recipe' : 'Save Recipe'}</Text>
            )}
          </TouchableOpacity>

          <View style={styles.navigationButtons}>
            <TouchableOpacity
              style={[styles.navButton, { backgroundColor: theme.secondary }]}
              onPress={() => navigation.navigate('Production')}
              activeOpacity={0.8}
            >
              <Text style={styles.navButtonText}>Go to Production</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, { backgroundColor: theme.accent }]}
              onPress={() => navigation.navigate('Dashboard')}
              activeOpacity={0.8}
            >
              <Text style={styles.navButtonText}>Go to Dashboard</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center'
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 15,
  },
  input: {
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    fontSize: 16,
  },
  ingredientRow: {
    flexDirection: 'column',
    marginBottom: 12,
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
  },
  rowTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  rowBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputFlex: {
    flex: 1,
    borderWidth: 1,
    padding: 10,
    marginHorizontal: 4,
    borderRadius: 6,
  },
  deleteButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  addIngredientButton: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  addMore: {
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
    elevation: 2,
  },
  saveButtonDisabled: {
    backgroundColor: '#a5d6a7',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  navButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
    elevation: 1,
  },
  navButtonText: {
    color: 'white',
    fontWeight: '500',
  },
});

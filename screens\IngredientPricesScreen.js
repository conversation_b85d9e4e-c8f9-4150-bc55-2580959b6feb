import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  FlatList,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  RefreshControl,
  Modal,
  ScrollView
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

export default function IngredientPricesScreen() {
  const [ingredients, setIngredients] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [tempPrices, setTempPrices] = useState({});
  const [priceHistoryModal, setPriceHistoryModal] = useState(false);
  const [selectedIngredient, setSelectedIngredient] = useState(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();

  useEffect(() => {
    loadIngredients();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  // Extract all unique ingredients from recipes
  const loadIngredients = async () => {
    try {
      setIsLoading(true);
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));

      // Get all ingredients from all recipes
      const allIngredients = [];
      const ingredientMap = new Map();

      for (let key of recipeKeys) {
        const raw = await AsyncStorage.getItem(key);
        const recipe = JSON.parse(raw);
        const productName = key.replace('recipe_', '');

        if (recipe.ingredients && recipe.ingredients.length > 0) {
          recipe.ingredients.forEach(ing => {
            const ingredientKey = `${ing.name}_${ing.unit || 'unit'}`;

            if (!ingredientMap.has(ingredientKey)) {
              ingredientMap.set(ingredientKey, {
                id: ingredientKey,
                name: ing.name,
                unit: ing.unit || 'unit',
                price: ing.price || '0',
                usedIn: [productName],
                priceHistory: ing.priceHistory || [],
                lastUpdated: ing.lastUpdated || null
              });
            } else {
              // Add product to usedIn if not already there
              const existingIng = ingredientMap.get(ingredientKey);
              if (!existingIng.usedIn.includes(productName)) {
                existingIng.usedIn.push(productName);
              }

              // Use the most recent price if available
              if (ing.lastUpdated && (!existingIng.lastUpdated || new Date(ing.lastUpdated) > new Date(existingIng.lastUpdated))) {
                existingIng.price = ing.price || existingIng.price;
                existingIng.lastUpdated = ing.lastUpdated;
              }
            }
          });
        }
      }

      // Convert map to array
      ingredientMap.forEach(value => {
        allIngredients.push(value);
      });

      // Sort by name
      allIngredients.sort((a, b) => a.name.localeCompare(b.name));

      setIngredients(allIngredients);
    } catch (error) {
      console.error('Error loading ingredients:', error);
      Alert.alert('Error', 'Failed to load ingredients');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadIngredients();
  };

  // Handle price change while typing
  const handlePriceChange = (ingredientId, price) => {
    setTempPrices(prev => ({
      ...prev,
      [ingredientId]: price
    }));
  };

  // Update price in all recipes that use this ingredient
  const updateIngredientPrice = async (ingredient, newPrice) => {
    try {
      setIsLoading(true);

      // Make sure the value is a valid number
      const numValue = parseFloat(newPrice);
      if (isNaN(numValue)) {
        Alert.alert('Error', 'Please enter a valid number');
        return;
      }

      // Get all recipes that use this ingredient
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));
      const now = new Date().toISOString();
      let updatedCount = 0;

      for (let key of recipeKeys) {
        const raw = await AsyncStorage.getItem(key);
        const recipe = JSON.parse(raw);
        let recipeUpdated = false;

        if (recipe.ingredients && recipe.ingredients.length > 0) {
          recipe.ingredients.forEach(ing => {
            if (ing.name === ingredient.name && (ing.unit || 'unit') === ingredient.unit) {
              // Add to price history if price changed
              if (ing.price !== newPrice) {
                if (!ing.priceHistory) ing.priceHistory = [];
                ing.priceHistory.push({
                  date: now,
                  oldPrice: ing.price || '0',
                  newPrice: newPrice
                });

                // Limit history to last 10 entries
                if (ing.priceHistory.length > 10) {
                  ing.priceHistory = ing.priceHistory.slice(-10);
                }

                ing.price = newPrice;
                ing.lastUpdated = now;
                recipeUpdated = true;
              }
            }
          });

          // Update perUnit calculations with new price
          if (recipeUpdated) {
            const servingsNum = parseFloat(recipe.servings);
            recipe.perUnit = recipe.ingredients.map(i => ({
              name: i.name,
              amount: parseFloat(i.amount) / servingsNum,
              unit: i.unit,
              stock: i.stock || parseFloat(i.amount) * 5,
              min: i.min || parseFloat(i.amount),
              price: i.price || '0',
            }));

            await AsyncStorage.setItem(key, JSON.stringify(recipe));
            updatedCount++;
          }
        }
      }

      // Success animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      // Update local state
      setIngredients(prev => {
        return prev.map(ing => {
          if (ing.id === ingredient.id) {
            // Add to price history
            const priceHistory = [...(ing.priceHistory || [])];
            priceHistory.push({
              date: now,
              oldPrice: ing.price || '0',
              newPrice: newPrice
            });

            // Limit history to last 10 entries
            if (priceHistory.length > 10) {
              priceHistory.slice(-10);
            }

            return {
              ...ing,
              price: newPrice,
              lastUpdated: now,
              priceHistory
            };
          }
          return ing;
        });
      });

      // Clear the temporary price
      setTempPrices(prev => {
        const newPrices = { ...prev };
        delete newPrices[ingredient.id];
        return newPrices;
      });

      Alert.alert('Success', `Price updated in ${updatedCount} recipes`);
    } catch (error) {
      console.error('Error updating ingredient price:', error);
      Alert.alert('Error', 'Failed to update ingredient price');
    } finally {
      setIsLoading(false);
    }
  };

  // Show price history modal
  const showPriceHistory = (ingredient) => {
    setSelectedIngredient(ingredient);
    setPriceHistoryModal(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <View style={styles.container}>
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Ingredient Prices</Text>
          <TouchableOpacity
            style={styles.manageButton}
            onPress={() => navigation.navigate('Ingredient Manager')}
          >
            <Text style={styles.manageButtonText}>Manage Ingredients</Text>
          </TouchableOpacity>
        </View>

        {/* Price History Modal */}
        <Modal
          visible={priceHistoryModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setPriceHistoryModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  Price History: {selectedIngredient?.name} ({selectedIngredient?.unit})
                </Text>
                <TouchableOpacity onPress={() => setPriceHistoryModal(false)}>
                  <MaterialIcons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.historyList}>
                {selectedIngredient?.priceHistory?.length > 0 ? (
                  selectedIngredient.priceHistory.slice().reverse().map((entry, index) => (
                    <View key={index} style={styles.historyItem}>
                      <Text style={styles.historyDate}>{formatDate(entry.date)}</Text>
                      <View style={styles.historyPriceChange}>
                        <Text style={styles.historyOldPrice}>₹{parseFloat(entry.oldPrice).toFixed(2)}</Text>
                        <MaterialIcons name="arrow-forward" size={16} color="#666" />
                        <Text style={styles.historyNewPrice}>₹{parseFloat(entry.newPrice).toFixed(2)}</Text>
                      </View>
                    </View>
                  ))
                ) : (
                  <Text style={styles.emptyHistory}>No price history available</Text>
                )}
              </ScrollView>

              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setPriceHistoryModal(false)}
              >
                <Text style={styles.closeButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {isLoading && !isRefreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4CAF50" />
            <Text style={styles.loadingText}>Loading ingredients...</Text>
          </View>
        ) : (
          <FlatList
            data={ingredients}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.card}>
                <View style={styles.cardHeader}>
                  <Text style={styles.ingredientName}>{item.name}</Text>
                  <Text style={styles.ingredientUnit}>({item.unit})</Text>
                </View>

                <View style={styles.priceRow}>
                  <Text style={styles.priceLabel}>Current Price:</Text>
                  <View style={styles.priceInputContainer}>
                    <Text style={styles.currencySymbol}>₹</Text>
                    <TextInput
                      style={styles.priceInput}
                      keyboardType="numeric"
                      value={tempPrices[item.id] !== undefined ? tempPrices[item.id] : (item.price?.toString() || '0')}
                      onChangeText={(text) => handlePriceChange(item.id, text)}
                      onEndEditing={(e) => updateIngredientPrice(item, e.nativeEvent.text)}
                      placeholder="0.00"
                    />
                    <Text style={styles.perUnitText}>/ {item.unit}</Text>
                  </View>
                </View>

                <View style={styles.usedInContainer}>
                  <Text style={styles.usedInLabel}>Used in:</Text>
                  <View style={styles.usedInList}>
                    {item.usedIn.map((product, index) => (
                      <View key={index} style={styles.usedInItem}>
                        <Text style={styles.usedInText}>{product}</Text>
                      </View>
                    ))}
                  </View>
                </View>

                <View style={styles.cardFooter}>
                  {item.lastUpdated ? (
                    <Text style={styles.lastUpdated}>
                      Last updated: {formatDate(item.lastUpdated)}
                    </Text>
                  ) : (
                    <Text style={styles.lastUpdated}>Never updated</Text>
                  )}

                  <TouchableOpacity
                    style={styles.historyButton}
                    onPress={() => showPriceHistory(item)}
                  >
                    <MaterialIcons name="history" size={16} color="#007BFF" />
                    <Text style={styles.historyButtonText}>History</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={['#4CAF50']}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No ingredients found</Text>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => navigation.navigate('Add Recipe')}
                >
                  <Text style={styles.addButtonText}>Add Your First Recipe</Text>
                </TouchableOpacity>
              </View>
            }
          />
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff'
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  manageButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  manageButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  card: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 1,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ingredientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  ingredientUnit: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#f5f5f5',
    padding: 10,
    borderRadius: 6,
  },
  priceLabel: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontSize: 16,
    color: '#333',
    marginRight: 4,
  },
  priceInput: {
    width: 80,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#fff',
    textAlign: 'center',
  },
  perUnitText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  usedInContainer: {
    marginBottom: 12,
  },
  usedInLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginBottom: 6,
  },
  usedInList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  usedInItem: {
    backgroundColor: '#e3f2fd',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 6,
    marginBottom: 6,
  },
  usedInText: {
    fontSize: 12,
    color: '#1565c0',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
  },
  historyButtonText: {
    fontSize: 14,
    color: '#007BFF',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  historyList: {
    maxHeight: 300,
  },
  historyItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  historyDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  historyPriceChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyOldPrice: {
    fontSize: 16,
    color: '#f44336',
    marginRight: 8,
  },
  historyNewPrice: {
    fontSize: 16,
    color: '#4caf50',
    marginLeft: 8,
  },
  emptyHistory: {
    padding: 20,
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
  },
  closeButton: {
    backgroundColor: '#007BFF',
    padding: 10,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 16,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

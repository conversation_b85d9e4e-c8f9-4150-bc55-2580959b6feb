import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
  Animated,
  SafeAreaView,
  StatusBar,
  TextInput
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import BillTemplate from '../components/BillTemplate';
import PrintService from '../utils/PrintService';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function BillingHistoryScreen() {
  const [sales, setSales] = useState([]);
  const [filteredSales, setFilteredSales] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSale, setSelectedSale] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [shopProfile, setShopProfile] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  useEffect(() => {
    loadSales();
    loadShopProfile();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    if (sales.length > 0) {
      filterSales();
    }
  }, [searchQuery, sales]);

  const loadSales = async () => {
    try {
      setLoading(true);
      const data = await AsyncStorage.getItem('sales_bills');
      const salesData = data ? JSON.parse(data) : [];
      // Sort sales by date (newest first)
      salesData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      setSales(salesData);
      setFilteredSales(salesData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load sales history');
    } finally {
      setLoading(false);
    }
  };

  const loadShopProfile = async () => {
    try {
      const shopProfileData = await AsyncStorage.getItem('shop_profile');
      if (shopProfileData) {
        setShopProfile(JSON.parse(shopProfileData));
      }
    } catch (error) {
      console.error('Failed to load shop profile:', error);
    }
  };

  const filterSales = () => {
    if (!searchQuery.trim()) {
      setFilteredSales(sales);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = sales.filter(sale =>
      sale.customerName.toLowerCase().includes(query) ||
      sale.id.includes(query) ||
      sale.items.some(item => item.name.toLowerCase().includes(query))
    );

    setFilteredSales(filtered);
  };

  const viewSaleDetails = (sale) => {
    setSelectedSale(sale);
    setModalVisible(true);
  };

  const deleteSale = async (id) => {
    try {
      const updatedSales = sales.filter(sale => sale.id !== id);
      await AsyncStorage.setItem('sales_bills', JSON.stringify(updatedSales));
      setSales(updatedSales);
      setModalVisible(false);
      Alert.alert('Success', 'Sale record deleted successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to delete sale record');
    }
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return dateString;
    }
  };

  const renderSaleItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.saleCard, { backgroundColor: theme.surface }]}
      onPress={() => viewSaleDetails(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.saleHeader, { backgroundColor: theme.card.background }]}>
        <Text style={[styles.saleDate, { color: theme.text.secondary }]}>{formatDate(item.createdAt)}</Text>
        <Text style={[styles.saleAmount, { color: theme.success }]}>₹{item.total.toFixed(2)}</Text>
      </View>

      <View style={styles.saleDetails}>
        <View style={styles.saleRow}>
          <Text style={[styles.saleLabel, { color: theme.text.secondary }]}>Customer:</Text>
          <Text style={[styles.saleValue, { color: theme.text.primary }]}>{item.customerName}</Text>
        </View>
        <View style={styles.saleRow}>
          <Text style={[styles.saleLabel, { color: theme.text.secondary }]}>Items:</Text>
          <Text style={[styles.saleValue, { color: theme.text.primary }]}>{item.items.length}</Text>
        </View>
        <View style={styles.saleRow}>
          <Text style={[styles.saleLabel, { color: theme.text.secondary }]}>Payment:</Text>
          <Text style={[styles.saleValue, { color: theme.text.primary }]}>
            {item.paymentMethod.charAt(0).toUpperCase() + item.paymentMethod.slice(1)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
          {/* Search Bar */}
          <View style={[styles.searchBar, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <MaterialIcons name="search" size={20} color={theme.text.secondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.text.primary }]}
              placeholder="Search by customer name or product..."
              placeholderTextColor={theme.text.hint}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <MaterialIcons name="clear" size={20} color={theme.text.secondary} />
              </TouchableOpacity>
            ) : null}
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.primary} />
              <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading sales history...</Text>
            </View>
          ) : filteredSales.length === 0 ? (
            <View style={[styles.emptyContainer, { backgroundColor: theme.surface }]}>
              <MaterialIcons name="point-of-sale" size={64} color={theme.text.disabled} />
              <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
                {searchQuery ? 'No sales records found matching your search' : 'No sales records found'}
              </Text>
              <TouchableOpacity
                style={[styles.addButton, { backgroundColor: theme.primary }]}
                onPress={() => navigation.navigate('ProductSelection')}
              >
                <Text style={styles.addButtonText}>Create Your First Sale</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={filteredSales}
              keyExtractor={(item) => item.id}
              renderItem={renderSaleItem}
              contentContainerStyle={styles.listContainer}
            />
          )}

          {/* Sale Detail Modal */}
          <Modal
            visible={modalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setModalVisible(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                {selectedSale && (
                  <>
                    <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                      <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Sale Details</Text>
                      <TouchableOpacity onPress={() => setModalVisible(false)}>
                        <MaterialIcons name="close" size={24} color={theme.text.primary} />
                      </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody}>
                      <BillTemplate
                        billData={selectedSale}
                        shopProfile={shopProfile}
                        theme={theme}
                      />
                    </ScrollView>

                    <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                      <TouchableOpacity
                        style={[styles.deleteButton, { backgroundColor: theme.error }]}
                        onPress={() => {
                          Alert.alert(
                            'Confirm Delete',
                            'Are you sure you want to delete this sale record?',
                            [
                              { text: 'Cancel', style: 'cancel' },
                              { text: 'Delete', onPress: () => deleteSale(selectedSale.id), style: 'destructive' }
                            ]
                          );
                        }}
                      >
                        <MaterialIcons name="delete" size={20} color="white" />
                        <Text style={styles.deleteButtonText}>Delete</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.printButton, { backgroundColor: theme.primary }]}
                        onPress={async () => {
                          try {
                            // First attempt direct printing
                            const result = await PrintService.directPrint(selectedSale, shopProfile);
                            if (!result.success) {
                              // If direct printing fails, show options
                              Alert.alert(
                                'Print Options',
                                'Choose a print option:',
                                [
                                  {
                                    text: 'Print Preview',
                                    onPress: async () => {
                                      const result = await PrintService.showPrintPreview(selectedSale, shopProfile);
                                      if (!result.success) {
                                        Alert.alert('Print Error', 'Failed to print bill. Please try again.');
                                      }
                                    }
                                  },
                                  {
                                    text: 'Save as PDF',
                                    onPress: async () => {
                                      const result = await PrintService.printToPDF(selectedSale, shopProfile);
                                      if (result.success) {
                                        Alert.alert('Success', result.message || 'Bill saved as PDF');
                                      } else {
                                        Alert.alert('Error', 'Failed to save bill as PDF. Please try again.');
                                      }
                                    }
                                  },
                                  {
                                    text: 'Save to Gallery',
                                    onPress: async () => {
                                      const result = await PrintService.saveAsImage(selectedSale, shopProfile);
                                      if (result.success) {
                                        Alert.alert('Success', result.message || 'Bill saved to gallery');
                                      } else {
                                        Alert.alert('Error', result.message || 'Failed to save bill to gallery. Please try again.');
                                      }
                                    }
                                  },
                                  { text: 'Cancel', style: 'cancel' }
                                ]
                              );
                            }
                          } catch (error) {
                            console.error('Print error:', error);
                            Alert.alert('Print Error', 'Failed to print bill. Please try again.');
                          }
                        }}
                      >
                        <MaterialIcons name="print" size={20} color="white" />
                        <Text style={styles.printButtonText}>Print Receipt</Text>
                      </TouchableOpacity>
                    </View>
                  </>
                )}
              </View>
            </View>
          </Modal>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 12,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    height: 44,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  listContainer: {
    paddingBottom: 16,
  },
  saleCard: {
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  saleDate: {
    fontSize: 14,
    fontWeight: '500',
  },
  saleAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  saleDetails: {
    padding: 12,
  },
  saleRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  saleLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 80,
  },
  saleValue: {
    fontSize: 14,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderRadius: 8,
  },
  emptyText: {
    fontSize: 18,
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  addButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '90%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
    maxHeight: 500,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  detailSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 16,
    fontWeight: '500',
    width: 120,
  },
  detailValue: {
    fontSize: 16,
    flex: 1,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemPrice: {
    fontSize: 14,
    marginTop: 4,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 16,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  deleteButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  printButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
  },
  printButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

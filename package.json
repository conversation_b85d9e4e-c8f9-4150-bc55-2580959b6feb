{"dependencies": {"@react-native-async-storage/async-storage": "1.17.11", "@react-native-community/datetimepicker": "6.7.3", "@react-native-picker/picker": "2.4.8", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@types/react": "~18.0.27", "expo": "^48.0.0", "expo-camera": "~13.2.1", "expo-constants": "~14.2.1", "expo-device": "~5.2.1", "expo-file-system": "~15.2.2", "expo-image-picker": "~14.1.1", "expo-media-library": "~15.2.3", "expo-notifications": "^0.18.1", "expo-print": "~12.2.1", "expo-sharing": "~11.2.2", "expo-status-bar": "~1.4.4", "firebase": "^9.23.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.9.0", "react-native-get-random-values": "~1.9.0", "react-native-pager-view": "6.1.2", "react-native-reanimated": "~2.14.4", "react-native-root-toast": "^3.4.1", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-share": "^12.0.9", "react-native-svg": "13.4.0", "react-native-vector-icons": "^9.2.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-flow-strip-types": "^7.26.5", "@types/react": "^18.0.27", "cross-env": "^7.0.3", "typescript": "^4.9.4"}, "name": "bakery", "version": "1.0.0", "main": "App.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": ""}
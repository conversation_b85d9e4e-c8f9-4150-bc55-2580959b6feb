import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function useLowStockCount() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const loadLowStock = async () => {
      try {
        const keys = await AsyncStorage.getAllKeys();
        const recipes = keys.filter(k => k.startsWith('recipe_'));
        let lowCount = 0;

        for (let key of recipes) {
          const raw = await AsyncStorage.getItem(key);
          const parsed = JSON.parse(raw);
          if (!parsed?.ingredients) continue;
          parsed.ingredients.forEach(i => {
            if (typeof i.stock === 'number' && typeof i.min === 'number' && i.stock < i.min) {
              lowCount++;
            }
          });
        }
        setCount(lowCount);
      } catch (e) {
        console.error('Failed to calculate low stock count', e);
        setCount(0);
      }
    };
    loadLowStock();
  }, []);

  return count;
}

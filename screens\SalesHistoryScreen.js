import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
  Animated
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function SalesHistoryScreen() {
  const [sales, setSales] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSale, setSelectedSale] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  useEffect(() => {
    loadSales();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadSales = async () => {
    try {
      setLoading(true);
      const data = await AsyncStorage.getItem('sales_bills');
      const salesData = data ? JSON.parse(data) : [];
      // Sort sales by date (newest first)
      salesData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      setSales(salesData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load sales history');
    } finally {
      setLoading(false);
    }
  };

  const viewSaleDetails = (sale) => {
    setSelectedSale(sale);
    setModalVisible(true);
  };

  const deleteSale = async (id) => {
    try {
      const updatedSales = sales.filter(sale => sale.id !== id);
      await AsyncStorage.setItem('sales_bills', JSON.stringify(updatedSales));
      setSales(updatedSales);
      setModalVisible(false);
      Alert.alert('Success', 'Sale record deleted successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to delete sale record');
    }
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return dateString;
    }
  };

  const renderSaleItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.saleCard, { backgroundColor: theme.surface }]}
      onPress={() => viewSaleDetails(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.saleHeader, { backgroundColor: theme.card.background }]}>
        <Text style={[styles.saleDate, { color: theme.text.secondary }]}>{formatDate(item.createdAt)}</Text>
        <Text style={[styles.saleAmount, { color: theme.success }]}>₹{item.total.toFixed(2)}</Text>
      </View>

      <View style={styles.saleDetails}>
        <View style={styles.saleRow}>
          <Text style={[styles.saleLabel, { color: theme.text.secondary }]}>Customer:</Text>
          <Text style={[styles.saleValue, { color: theme.text.primary }]}>{item.customerName}</Text>
        </View>
        <View style={styles.saleRow}>
          <Text style={[styles.saleLabel, { color: theme.text.secondary }]}>Items:</Text>
          <Text style={[styles.saleValue, { color: theme.text.primary }]}>{item.items.length}</Text>
        </View>
        <View style={styles.saleRow}>
          <Text style={[styles.saleLabel, { color: theme.text.secondary }]}>Payment:</Text>
          <Text style={[styles.saleValue, { color: theme.text.primary }]}>
            {item.paymentMethod.charAt(0).toUpperCase() + item.paymentMethod.slice(1)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <Text style={[styles.title, { color: theme.text.primary }]}>Sales History</Text>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading sales history...</Text>
          </View>
        ) : sales.length === 0 ? (
          <View style={[styles.emptyContainer, { backgroundColor: theme.surface }]}>
            <MaterialIcons name="point-of-sale" size={64} color={theme.text.disabled} />
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No sales records found</Text>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.primary }]}
              onPress={() => navigation.navigate('Billing')}
            >
              <Text style={styles.addButtonText}>Create Your First Sale</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={sales}
            keyExtractor={(item) => item.id}
            renderItem={renderSaleItem}
            contentContainerStyle={styles.listContainer}
          />
        )}

        {/* Sale Detail Modal */}
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
              {selectedSale && (
                <>
                  <View style={[styles.modalHeader, { borderBottomColor: theme.border }]}>
                    <Text style={[styles.modalTitle, { color: theme.text.primary }]}>Sale Details</Text>
                    <TouchableOpacity onPress={() => setModalVisible(false)}>
                      <MaterialIcons name="close" size={24} color={theme.text.primary} />
                    </TouchableOpacity>
                  </View>

                  <ScrollView style={styles.modalBody}>
                    <View style={styles.detailSection}>
                      <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Sale Information</Text>
                      
                      <View style={styles.detailRow}>
                        <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Date:</Text>
                        <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                          {formatDate(selectedSale.createdAt)}
                        </Text>
                      </View>

                      <View style={styles.detailRow}>
                        <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Customer:</Text>
                        <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                          {selectedSale.customerName}
                        </Text>
                      </View>

                      {selectedSale.customerPhone && selectedSale.customerPhone !== 'N/A' && (
                        <View style={styles.detailRow}>
                          <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Phone:</Text>
                          <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                            {selectedSale.customerPhone}
                          </Text>
                        </View>
                      )}

                      <View style={styles.detailRow}>
                        <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Payment Method:</Text>
                        <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                          {selectedSale.paymentMethod.charAt(0).toUpperCase() + selectedSale.paymentMethod.slice(1)}
                        </Text>
                      </View>

                      <View style={styles.detailRow}>
                        <Text style={[styles.detailLabel, { color: theme.text.secondary }]}>Created By:</Text>
                        <Text style={[styles.detailValue, { color: theme.text.primary }]}>
                          {selectedSale.createdBy.charAt(0).toUpperCase() + selectedSale.createdBy.slice(1)}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.detailSection}>
                      <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Items</Text>
                      
                      {selectedSale.items.map((item, index) => (
                        <View key={index} style={[styles.itemRow, { borderBottomColor: theme.border }]}>
                          <View style={styles.itemInfo}>
                            <Text style={[styles.itemName, { color: theme.text.primary }]}>
                              {item.name} × {item.quantity}
                            </Text>
                            <Text style={[styles.itemPrice, { color: theme.text.secondary }]}>
                              ₹{item.price.toFixed(2)} each
                            </Text>
                          </View>
                          <Text style={[styles.itemTotal, { color: theme.success }]}>
                            ₹{item.subtotal.toFixed(2)}
                          </Text>
                        </View>
                      ))}

                      <View style={[styles.totalRow, { borderTopColor: theme.border }]}>
                        <Text style={[styles.totalLabel, { color: theme.text.primary }]}>Total:</Text>
                        <Text style={[styles.totalAmount, { color: theme.success }]}>
                          ₹{selectedSale.total.toFixed(2)}
                        </Text>
                      </View>
                    </View>
                  </ScrollView>

                  <View style={[styles.modalFooter, { borderTopColor: theme.border }]}>
                    <TouchableOpacity
                      style={[styles.deleteButton, { backgroundColor: theme.error }]}
                      onPress={() => {
                        Alert.alert(
                          'Confirm Delete',
                          'Are you sure you want to delete this sale record?',
                          [
                            { text: 'Cancel', style: 'cancel' },
                            { text: 'Delete', onPress: () => deleteSale(selectedSale.id), style: 'destructive' }
                          ]
                        );
                      }}
                    >
                      <MaterialIcons name="delete" size={20} color="white" />
                      <Text style={styles.deleteButtonText}>Delete</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.printButton, { backgroundColor: theme.primary }]}
                      onPress={() => Alert.alert('Print', 'Printing functionality would be implemented here')}
                    >
                      <MaterialIcons name="print" size={20} color="white" />
                      <Text style={styles.printButtonText}>Print Receipt</Text>
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </View>
          </View>
        </Modal>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  saleCard: {
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  saleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  saleDate: {
    fontSize: 14,
    fontWeight: '500',
  },
  saleAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  saleDetails: {
    padding: 12,
  },
  saleRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  saleLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 80,
  },
  saleValue: {
    fontSize: 14,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderRadius: 8,
  },
  emptyText: {
    fontSize: 18,
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  addButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    maxHeight: '80%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
    maxHeight: 500,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  detailSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 16,
    fontWeight: '500',
    width: 120,
  },
  detailValue: {
    fontSize: 16,
    flex: 1,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemPrice: {
    fontSize: 14,
    marginTop: 4,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 16,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  deleteButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  printButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
  },
  printButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

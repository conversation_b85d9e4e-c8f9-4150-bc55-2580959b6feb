import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function useProductionCount() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const loadCount = async () => {
      try {
        const raw = await AsyncStorage.getItem('production_data');
        const data = JSON.parse(raw) || [];
        setCount(data.length);
      } catch (e) {
        console.error('Failed to load production count', e);
        setCount(0);
      }
    };
    loadCount();
  }, []);

  return count;
}

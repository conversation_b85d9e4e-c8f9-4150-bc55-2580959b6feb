import mongoose from 'mongoose';
import models from '../models/index';

// Simple function to test if models are imported correctly
export const testModels = () => {
  console.log('Testing model imports:');
  console.log('Models object:', models ? 'Loaded' : 'Not loaded');
  console.log('Product model:', models.Product ? 'Loaded' : 'Not loaded');
  console.log('Ingredient model:', models.Ingredient ? 'Loaded' : 'Not loaded');
  console.log('Production model:', models.Production ? 'Loaded' : 'Not loaded');
  console.log('Analytics model:', models.Analytics ? 'Loaded' : 'Not loaded');
  console.log('Bill model:', models.Bill ? 'Loaded' : 'Not loaded');
  console.log('IngredientBill model:', models.IngredientBill ? 'Loaded' : 'Not loaded');
  console.log('ShopProfile model:', models.ShopProfile ? 'Loaded' : 'Not loaded');
  console.log('User model:', models.User ? 'Loaded' : 'Not loaded');

  return models;
};

import * as Print from 'expo-print';
import { shareAsync } from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { Platform, Alert } from 'react-native';

/**
 * Service for handling bill printing and PDF generation
 */
export default class PrintService {
  /**
   * Generate HTML content for a bill
   * @param {Object} billData - The bill data
   * @param {Object} shopProfile - The shop profile data
   * @returns {string} HTML content
   */
  static generateBillHTML(billData, shopProfile) {
    // Format date
    const formatDate = (dateString) => {
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } catch (e) {
        return dateString;
      }
    };

    // Get a random quote from the shop profile
    const getRandomQuote = () => {
      if (!shopProfile?.quotes || shopProfile.quotes.length === 0) {
        return "Thank you for your business!";
      }
      const randomIndex = Math.floor(Math.random() * shopProfile.quotes.length);
      return shopProfile.quotes[randomIndex];
    };

    // Generate items table rows
    const generateItemRows = () => {
      return billData.items.map((item, index) => `
        <tr>
          <td style="padding: 8px; border-bottom: 1px solid #ddd;">${item.name}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${item.quantity}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">₹${item.price.toFixed(2)}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">₹${item.subtotal.toFixed(2)}</td>
        </tr>
      `).join('');
    };

    // HTML template
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
          <style>
            @page {
              size: A4;
              margin: 0;
            }
            body {
              font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
              padding: 20px;
              max-width: 600px;
              margin: 0 auto;
              color: #333;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
            .shop-header {
              text-align: center;
              margin-bottom: 20px;
            }
            .shop-name {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .shop-address {
              margin-bottom: 5px;
              font-size: 14px;
            }
            .shop-contact {
              font-size: 12px;
              margin-bottom: 5px;
            }
            .shop-gst {
              font-size: 12px;
            }
            .bill-info {
              border: 1px solid #ddd;
              border-radius: 5px;
              padding: 15px;
              margin-bottom: 20px;
            }
            .bill-info-row {
              display: flex;
              margin-bottom: 5px;
            }
            .bill-info-label {
              width: 120px;
              font-weight: 500;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            .table-header {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            .table-footer {
              font-weight: bold;
              border-top: 2px solid #ddd;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              font-size: 14px;
            }
            .footer-quote {
              font-style: italic;
              margin-bottom: 10px;
            }
            .footer-text {
              font-size: 10px;
              color: #999;
            }
          </style>
        </head>
        <body>
          <!-- Shop Header -->
          <div class="shop-header">
            <div class="shop-name">${shopProfile?.shopName || 'Bakery Shop'}</div>
            ${shopProfile?.address ? `<div class="shop-address">${shopProfile.address}</div>` : ''}
            <div class="shop-contact">
              ${shopProfile?.phone ? `Phone: ${shopProfile.phone}` : ''}
              ${shopProfile?.email ? ` | Email: ${shopProfile.email}` : ''}
            </div>
            ${shopProfile?.gstNumber ? `<div class="shop-gst">GST No: ${shopProfile.gstNumber}</div>` : ''}
          </div>

          <!-- Bill Info -->
          <div class="bill-info">
            <div class="bill-info-row">
              <div class="bill-info-label">Bill No:</div>
              <div>${billData.id}</div>
            </div>
            <div class="bill-info-row">
              <div class="bill-info-label">Date:</div>
              <div>${formatDate(billData.createdAt)}</div>
            </div>
            <div class="bill-info-row">
              <div class="bill-info-label">Customer:</div>
              <div>${billData.customerName}</div>
            </div>
            ${billData.customerPhone && billData.customerPhone !== 'N/A' ? `
              <div class="bill-info-row">
                <div class="bill-info-label">Phone:</div>
                <div>${billData.customerPhone}</div>
              </div>
            ` : ''}
            <div class="bill-info-row">
              <div class="bill-info-label">Payment:</div>
              <div>${billData.paymentMethod.charAt(0).toUpperCase() + billData.paymentMethod.slice(1)}</div>
            </div>
          </div>

          <!-- Items Table -->
          <table class="items-table">
            <thead>
              <tr class="table-header">
                <th style="padding: 8px; text-align: left;">Item</th>
                <th style="padding: 8px; text-align: center;">Qty</th>
                <th style="padding: 8px; text-align: right;">Price</th>
                <th style="padding: 8px; text-align: right;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${generateItemRows()}
            </tbody>
            <tfoot>
              <tr class="table-footer">
                <td colspan="3" style="padding: 12px; text-align: right;">Total Amount:</td>
                <td style="padding: 12px; text-align: right;">₹${billData.total.toFixed(2)}</td>
              </tr>
            </tfoot>
          </table>

          <!-- Footer -->
          <div class="footer">
            <div class="footer-quote">"${getRandomQuote()}"</div>
            <div class="footer-text">This is a computer-generated bill and does not require a signature.</div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Print a bill to PDF
   * @param {Object} billData - The bill data
   * @param {Object} shopProfile - The shop profile data
   * @returns {Promise<void>}
   */
  static async printToPDF(billData, shopProfile) {
    try {
      // Generate HTML
      const html = this.generateBillHTML(billData, shopProfile);

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        width: 612, // Standard A4 width in points (8.5 x 72)
        height: 792, // Standard A4 height in points (11 x 72)
      });

      // On iOS/macOS, print the PDF
      if (Platform.OS === 'ios') {
        await Print.printAsync({ uri });
      }
      // On Android, save and share the PDF
      else {
        // Get permissions to save to media library
        const { status } = await MediaLibrary.requestPermissionsAsync();

        if (status === 'granted') {
          // Create a filename with date and bill ID
          const fileName = `Bill_${billData.id}_${new Date().toISOString().split('T')[0]}.pdf`;
          const fileUri = FileSystem.documentDirectory + fileName;

          // Copy the file to a permanent location
          await FileSystem.copyAsync({
            from: uri,
            to: fileUri
          });

          // Save to media library
          const asset = await MediaLibrary.createAssetAsync(fileUri);
          await MediaLibrary.createAlbumAsync('Bakery Bills', asset, false);

          // Share the file
          await shareAsync(fileUri, { UTI: '.pdf', mimeType: 'application/pdf' });

          return { success: true, uri: fileUri, message: 'PDF saved to Bakery Bills folder and shared' };
        } else {
          // If permission not granted, just share the temporary file
          await shareAsync(uri, { UTI: '.pdf', mimeType: 'application/pdf' });
          return { success: true, uri, message: 'PDF shared (not saved to device)' };
        }
      }

      return { success: true, uri };
    } catch (error) {
      console.error('Error printing bill:', error);
      return { success: false, error };
    }
  }

  /**
   * Show print preview
   * @param {Object} billData - The bill data
   * @param {Object} shopProfile - The shop profile data
   * @returns {Promise<void>}
   */
  static async showPrintPreview(billData, shopProfile) {
    try {
      // Generate HTML
      const html = this.generateBillHTML(billData, shopProfile);

      // Show print preview
      const result = await Print.printAsync({
        html,
        width: 612, // Standard A4 width in points (8.5 x 72)
        height: 792, // Standard A4 height in points (11 x 72)
        printerUrl: undefined, // Let the user select a printer
      });

      return { success: true, result };
    } catch (error) {
      console.error('Error showing print preview:', error);
      return { success: false, error };
    }
  }

  /**
   * Save bill as image
   * @param {Object} billData - The bill data
   * @param {Object} shopProfile - The shop profile data
   * @returns {Promise<void>}
   */
  static async saveAsImage(billData, shopProfile) {
    try {
      // Generate HTML
      const html = this.generateBillHTML(billData, shopProfile);

      // Create PDF first (we'll convert it to image)
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        width: 612, // Standard A4 width in points (8.5 x 72)
        height: 792, // Standard A4 height in points (11 x 72)
      });

      // Get permissions to save to media library
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status === 'granted') {
        // Create a filename with date and bill ID
        const fileName = `Bill_${billData.id}_${new Date().toISOString().split('T')[0]}.pdf`;
        const fileUri = FileSystem.documentDirectory + fileName;

        // Copy the file to a permanent location
        await FileSystem.copyAsync({
          from: uri,
          to: fileUri
        });

        // Save to media library
        const asset = await MediaLibrary.createAssetAsync(fileUri);
        await MediaLibrary.createAlbumAsync('Bakery Bills', asset, false);

        return { success: true, uri: fileUri, message: 'Bill saved to Bakery Bills folder' };
      } else {
        return { success: false, message: 'Permission to save file was denied' };
      }
    } catch (error) {
      console.error('Error saving bill as image:', error);
      return { success: false, error };
    }
  }

  /**
   * Direct print function - prints immediately without showing options
   * @param {Object} billData - The bill data
   * @param {Object} shopProfile - The shop profile data
   * @returns {Promise<void>}
   */
  static async directPrint(billData, shopProfile) {
    try {
      // Generate HTML
      const html = this.generateBillHTML(billData, shopProfile);

      // Print directly
      await Print.printAsync({
        html,
        width: 612, // Standard A4 width in points (8.5 x 72)
        height: 792, // Standard A4 height in points (11 x 72)
      });

      return { success: true, message: 'Bill printed successfully' };
    } catch (error) {
      console.error('Error printing bill:', error);
      return { success: false, error };
    }
  }
}

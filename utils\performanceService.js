import { initializeApp } from 'firebase/app';
import { getPerformance, trace } from 'firebase/performance';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeFirebase, getFirebaseApp, firebaseConfig } from '../firebase';
import remoteConfigService from './remoteConfigService';

// Flag to track if we've already logged the performance warning
let performanceWarningLogged = false;

// Initialize Performance Monitoring
let performance = null;
let activeTraces = {};

export const initializePerformance = async () => {
  try {
    // Check if performance monitoring is enabled in remote config
    const enabled = await remoteConfigService.getConfigValue('enable_performance_monitoring', true);
    if (!enabled) {
      console.log('Performance monitoring is disabled in remote config');
      return { success: false, error: 'Performance monitoring is disabled' };
    }

    // Get Firebase app instance
    let app = getFirebaseApp();
    if (!app) {
      try {
        const result = await initializeFirebase();
        if (!result || !result.success) {
          if (!performanceWarningLogged) {
            console.log('Failed to initialize Firebase for performance monitoring, will try direct initialization');
            performanceWarningLogged = true;
          }

          // Try direct initialization as a fallback
          app = initializeApp(firebaseConfig);
        } else {
          app = getFirebaseApp();
        }
      } catch (initError) {
        console.error('Error during Firebase initialization:', initError);
        return { success: false, error: 'Failed to initialize Firebase: ' + initError.message };
      }
    }

    // Initialize Performance Monitoring
    performance = getPerformance(app);

    console.log('Performance monitoring initialized successfully');
    return { success: true };
  } catch (error) {
    console.error('Error initializing Performance Monitoring:', error);
    return { success: false, error: error.message };
  }
};

// Start a trace
export const startTrace = async (traceName) => {
  try {
    if (!performance) {
      const result = await initializePerformance();
      if (!result.success) {
        return { success: false, error: result.error };
      }
    }

    // Create a trace
    const newTrace = trace(performance, traceName);

    // Start the trace
    newTrace.start();

    // Store the trace
    activeTraces[traceName] = newTrace;

    return { success: true, trace: newTrace };
  } catch (error) {
    console.error(`Error starting trace ${traceName}:`, error);
    return { success: false, error: error.message };
  }
};

// Stop a trace
export const stopTrace = async (traceName) => {
  try {
    if (!activeTraces[traceName]) {
      return { success: false, error: `Trace ${traceName} not found` };
    }

    // Stop the trace
    activeTraces[traceName].stop();

    // Remove from active traces
    delete activeTraces[traceName];

    return { success: true };
  } catch (error) {
    console.error(`Error stopping trace ${traceName}:`, error);
    return { success: false, error: error.message };
  }
};

// Add a custom attribute to a trace
export const putTraceAttribute = async (traceName, attributeName, attributeValue) => {
  try {
    if (!activeTraces[traceName]) {
      return { success: false, error: `Trace ${traceName} not found` };
    }

    // Add attribute
    activeTraces[traceName].putAttribute(attributeName, attributeValue);

    return { success: true };
  } catch (error) {
    console.error(`Error adding attribute to trace ${traceName}:`, error);
    return { success: false, error: error.message };
  }
};

// Increment a custom metric in a trace
export const incrementTraceMetric = async (traceName, metricName, incrementBy = 1) => {
  try {
    if (!activeTraces[traceName]) {
      return { success: false, error: `Trace ${traceName} not found` };
    }

    // Increment metric
    activeTraces[traceName].incrementMetric(metricName, incrementBy);

    return { success: true };
  } catch (error) {
    console.error(`Error incrementing metric in trace ${traceName}:`, error);
    return { success: false, error: error.message };
  }
};

// Record a screen view
export const recordScreenView = async (screenName) => {
  try {
    // Start a trace for the screen view
    const { success, trace: screenTrace } = await startTrace(`screen_view_${screenName}`);

    if (!success) {
      return { success: false, error: 'Failed to start trace for screen view' };
    }

    // Add screen name attribute
    await putTraceAttribute(`screen_view_${screenName}`, 'screen_name', screenName);

    // Stop the trace after a short delay (to capture initial render time)
    setTimeout(async () => {
      await stopTrace(`screen_view_${screenName}`);
    }, 1000);

    return { success: true };
  } catch (error) {
    console.error(`Error recording screen view for ${screenName}:`, error);
    return { success: false, error: error.message };
  }
};

// Measure function execution time
export const measureFunction = async (functionName, func, ...args) => {
  try {
    // Start a trace
    const { success } = await startTrace(`function_${functionName}`);

    if (!success) {
      // If trace fails, just execute the function
      return await func(...args);
    }

    try {
      // Execute the function
      const result = await func(...args);

      // Stop the trace
      await stopTrace(`function_${functionName}`);

      return result;
    } catch (error) {
      // Add error attribute
      await putTraceAttribute(`function_${functionName}`, 'error', error.message);

      // Stop the trace
      await stopTrace(`function_${functionName}`);

      // Re-throw the error
      throw error;
    }
  } catch (error) {
    console.error(`Error measuring function ${functionName}:`, error);
    throw error;
  }
};

// Track network request
export const trackNetworkRequest = async (url, method, startTime, endTime, responseCode, responseSize) => {
  try {
    // Create a trace name based on URL
    const urlObj = new URL(url);
    const traceName = `network_${method}_${urlObj.hostname}${urlObj.pathname}`;

    // Start a trace
    const { success, trace: networkTrace } = await startTrace(traceName);

    if (!success) {
      return { success: false, error: 'Failed to start trace for network request' };
    }

    // Add attributes
    await putTraceAttribute(traceName, 'url', url);
    await putTraceAttribute(traceName, 'method', method);
    await putTraceAttribute(traceName, 'response_code', responseCode.toString());

    // Add metrics
    await incrementTraceMetric(traceName, 'response_size', responseSize);
    await incrementTraceMetric(traceName, 'response_time', endTime - startTime);

    // Stop the trace
    await stopTrace(traceName);

    return { success: true };
  } catch (error) {
    console.error('Error tracking network request:', error);
    return { success: false, error: error.message };
  }
};

export default {
  initializePerformance,
  startTrace,
  stopTrace,
  putTraceAttribute,
  incrementTraceMetric,
  recordScreenView,
  measureFunction,
  trackNetworkRequest
};

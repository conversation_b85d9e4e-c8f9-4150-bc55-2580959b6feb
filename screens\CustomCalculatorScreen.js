import React, { useState, useEffect } from 'react';
import * as Print from 'expo-print';

import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Picker } from '@react-native-picker/picker';

export default function CustomCalculatorScreen() {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [units, setUnits] = useState('');
  const [output, setOutput] = useState('');

  useEffect(() => {
    loadProductList();
  }, []);

  const loadProductList = async () => {
    const keys = await AsyncStorage.getAllKeys();
    const productKeys = keys.filter(k => k.startsWith('recipe_'));
    const productNames = productKeys.map(k => k.replace('recipe_', ''));
    setProducts(productNames);
  };

  const calculate = async () => {
    if (!selectedProduct || !units) return Alert.alert('Please complete all fields');
    const data = await AsyncStorage.getItem(`recipe_${selectedProduct}`);
    if (!data) return Alert.alert('Recipe not found');

    const recipe = JSON.parse(data);
    const qty = parseFloat(units);
    let result = `For ${qty} units of ${selectedProduct}:
`;
    recipe.ingredients.forEach(i => {
      const perUnit = i.amount / recipe.servings;
      const total = perUnit * qty;
      result += `\n- ${i.name}: ${total.toFixed(2)} ${i.unit}`;
    });
    setOutput(result);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Ingredient Calculator</Text>
      <Picker
        selectedValue={selectedProduct}
        onValueChange={setSelectedProduct}
        style={styles.input}
      >
        <Picker.Item label="Select Product" value="" />
        {products.map(name => (
          <Picker.Item key={name} label={name} value={name} />
        ))}
      </Picker>
      <TextInput
        style={styles.input}
        placeholder="Enter quantity"
        keyboardType="numeric"
        value={units}
        onChangeText={setUnits}
      />
      <Button title="Calculate" onPress={calculate} color="#2196F3" />
      <Text style={styles.output}>{output}</Text>
      <Button
  title="Export to PDF"
  onPress={async () => {
    if (!output) return Alert.alert('Nothing to export');
    try {
      const html = `<pre style='font-size: 16px;'>${output.replace(/\n/g, '<br>')}</pre>`;
      await Print.printAsync({ html });
    } catch (e) {
      Alert.alert('Export Failed', 'Could not generate PDF');
    }
  }}
  color="#9C27B0"
/>

      </View>
    );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, backgroundColor: '#fff' },
  title: { fontSize: 22, fontWeight: 'bold', marginBottom: 16 },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 6,
    marginBottom: 12,
  },
  output: {
    marginTop: 20,
    fontSize: 16,
    backgroundColor: '#f1f1f1',
    padding: 10,
    borderRadius: 6,
  },
});

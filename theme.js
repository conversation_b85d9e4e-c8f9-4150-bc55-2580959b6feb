// Theme configuration for the app
export const lightTheme = {
  // Base colors
  background: '#f8f9fa',
  surface: '#ffffff',
  primary: '#4CAF50',
  secondary: '#2196F3',
  accent: '#FF9800',
  error: '#F44336',
  warning: '#FFC107',
  info: '#03A9F4',
  success: '#4CAF50',

  // Text colors
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#9E9E9E',
    hint: '#9E9E9E',
  },

  // Card colors
  card: {
    background: '#ffffff',
    statsBlue: '#e0f7fa',
    statsGreen: '#e8f5e9',
    statsRed: '#ffebee',
    statsYellow: '#fff8e1',
    shadow: 'rgba(0, 0, 0, 0.1)',
  },

  // Border colors
  border: '#e0e0e0',

  // Icon colors
  icon: {
    active: '#4CAF50',
    inactive: '#9E9E9E',
  },

  // Input colors
  input: {
    background: '#f5f5f5',
  },

  // Status colors
  status: {
    lowStock: '#F44336',
    inStock: '#4CAF50',
  },
};

export const darkTheme = {
  // Base colors
  background: '#121212',
  surface: '#1e1e1e',
  primary: '#81C784',
  secondary: '#64B5F6',
  accent: '#FFB74D',
  error: '#E57373',
  warning: '#FFD54F',
  info: '#4FC3F7',
  success: '#81C784',

  // Text colors
  text: {
    primary: '#FFFFFF',
    secondary: '#B0BEC5',
    disabled: '#78909C',
    hint: '#78909C',
  },

  // Card colors
  card: {
    background: '#1e1e1e',
    statsBlue: '#01579B',
    statsGreen: '#1B5E20',
    statsRed: '#B71C1C',
    statsYellow: '#F57F17',
    shadow: 'rgba(0, 0, 0, 0.3)',
  },

  // Border colors
  border: '#333333',

  // Icon colors
  icon: {
    active: '#81C784',
    inactive: '#78909C',
  },

  // Input colors
  input: {
    background: '#333333',
  },

  // Status colors
  status: {
    lowStock: '#E57373',
    inStock: '#81C784',
  },
};

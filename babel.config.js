module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Add any additional Babel plugins here
      ['module-resolver', {
        root: ['./'],
        extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
        alias: {
          '@components': './components',
          '@screens': './screens',
          '@utils': './utils',
          '@assets': './assets',
          '@firebase': './firebase.js',
        },
      }],
      // Support for TypeScript decorators
      ['@babel/plugin-proposal-decorators', { legacy: true }],
      // Support for class properties
      ['@babel/plugin-proposal-class-properties', { loose: true }],
      // Support for private methods
      ['@babel/plugin-proposal-private-methods', { loose: true }],
      // Support for private property in class
      ['@babel/plugin-proposal-private-property-in-object', { loose: true }],
    ],
  };
};

import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  FlatList,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  RefreshControl
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';

export default function ProductPricingScreen() {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [tempPrices, setTempPrices] = useState({});
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  // Calculate ingredient cost for a product
  const calculateIngredientCost = (product) => {
    let total = 0;
    if (product.ingredients && product.ingredients.length > 0) {
      product.ingredients.forEach(ing => {
        const price = parseFloat(ing.price) || 0;
        const amount = parseFloat(ing.amount) || 0;
        total += price * amount / product.servings;
      });
    }
    return total;
  };

  // Calculate profit margin
  const calculateProfitMargin = (product) => {
    const cost = calculateIngredientCost(product);
    const price = parseFloat(product.sellingPrice) || 0;
    if (cost === 0 || price === 0) return 0;
    return ((price - cost) / price) * 100;
  };

  // Calculate profit amount
  const calculateProfit = (product) => {
    const cost = calculateIngredientCost(product);
    const price = parseFloat(product.sellingPrice) || 0;
    return price - cost;
  };

  useEffect(() => {
    loadProducts();

    // Start fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));
      const recipeData = [];

      for (let key of recipeKeys) {
        const raw = await AsyncStorage.getItem(key);
        const parsed = JSON.parse(raw);
        recipeData.push({
          name: key.replace('recipe_', ''),
          ...parsed,
          sellingPrice: parsed.sellingPrice || '0'
        });
      }

      setProducts(recipeData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadProducts();
  };

  // Handle price change while typing
  const handlePriceChange = (productName, price) => {
    setTempPrices(prev => ({
      ...prev,
      [productName]: price
    }));
  };

  // Update price in AsyncStorage when editing is complete
  const updateProductPrice = async (productName, price) => {
    try {
      setIsLoading(true);
      const key = `recipe_${productName}`;
      const raw = await AsyncStorage.getItem(key);
      const recipe = JSON.parse(raw);

      // Make sure the value is a valid number
      const numValue = parseFloat(price);
      if (isNaN(numValue)) {
        Alert.alert('Error', 'Please enter a valid number');
        return;
      }

      recipe.sellingPrice = price;
      await AsyncStorage.setItem(key, JSON.stringify(recipe));

      // Success animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      // Update the products list with the new price
      setProducts(prevProducts => {
        return prevProducts.map(p => {
          if (p.name === productName) {
            return { ...p, sellingPrice: price };
          }
          return p;
        });
      });

      // Clear the temporary price for this product
      setTempPrices(prev => {
        const newPrices = { ...prev };
        delete newPrices[productName];
        return newPrices;
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to update product price');
    } finally {
      setIsLoading(false);
    }
  };

  const suggestPrice = (product) => {
    const cost = calculateIngredientCost(product);
    // Suggest a price with 30% profit margin
    const suggestedPrice = cost * 1.43; // 30% profit margin

    Alert.alert(
      'Price Suggestion',
      `Based on ingredient costs (₹${cost.toFixed(2)}), we suggest:\n\n` +
      `₹${suggestedPrice.toFixed(2)} (30% profit margin)\n` +
      `₹${(cost * 1.67).toFixed(2)} (40% profit margin)\n` +
      `₹${(cost * 2).toFixed(2)} (50% profit margin)`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Use 30%',
          onPress: () => updateProductPrice(product.name, suggestedPrice.toFixed(2))
        },
        {
          text: 'Use 40%',
          onPress: () => updateProductPrice(product.name, (cost * 1.67).toFixed(2))
        },
        {
          text: 'Use 50%',
          onPress: () => updateProductPrice(product.name, (cost * 2).toFixed(2))
        }
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
        <Text style={[styles.title, { color: theme.text.primary }]}>Product Pricing</Text>

        {/* Total Profit Summary */}
        <View style={[styles.summaryBox, { backgroundColor: theme.card.statsGreen }]}>
          <Text style={[styles.summaryTitle, { color: theme.isDarkMode ? '#fff' : '#2e7d32' }]}>💰 Profit Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.text.secondary }]}>Total Products:</Text>
            <Text style={[styles.summaryValue, { color: theme.isDarkMode ? '#fff' : '#2e7d32' }]}>{products.length}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.text.secondary }]}>Avg. Profit Margin:</Text>
            <Text style={[styles.summaryValue, { color: theme.isDarkMode ? '#fff' : '#2e7d32' }]}>
              {products.length > 0
                ? (products.reduce((sum, p) => sum + calculateProfitMargin(p), 0) / products.length).toFixed(2)
                : 0}%
            </Text>
          </View>
        </View>

        {isLoading && !isRefreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading products...</Text>
          </View>
        ) : (
          <FlatList
            data={products}
            keyExtractor={(item) => item.name}
            renderItem={({ item }) => {
              // Use the temporary price if it exists, otherwise use the stored price
              const effectiveItem = {
                ...item,
                sellingPrice: tempPrices[item.name] !== undefined ? tempPrices[item.name] : item.sellingPrice
              };

              const costPrice = calculateIngredientCost(item);
              const profit = calculateProfit(effectiveItem);
              const margin = calculateProfitMargin(effectiveItem);

              return (
                <View style={[styles.card, { backgroundColor: theme.surface }]}>
                  <Text style={[styles.productTitle, { color: theme.text.primary }]}>{item.name}</Text>

                  <View style={styles.costRow}>
                    <Text style={[styles.costLabel, { color: theme.text.secondary }]}>Ingredient Cost:</Text>
                    <Text style={[styles.costValue, { color: theme.text.secondary }]}>₹{costPrice.toFixed(2)}</Text>
                  </View>

                  <View style={[styles.priceRow, { backgroundColor: theme.card.background }]}>
                    <Text style={[styles.priceLabel, { color: theme.text.primary }]}>Selling Price:</Text>
                    <TextInput
                      style={[styles.priceInput, { backgroundColor: theme.background, borderColor: theme.border, color: theme.text.primary }]}
                      placeholderTextColor={theme.text.hint}
                      keyboardType="numeric"
                      value={tempPrices[item.name] !== undefined ? tempPrices[item.name] : (item.sellingPrice?.toString() || '0')}
                      onChangeText={(text) => handlePriceChange(item.name, text)}
                      onEndEditing={(e) => updateProductPrice(item.name, e.nativeEvent.text)}
                      placeholder="0.00"
                    />
                  </View>

                  <View style={styles.profitRow}>
                    <View style={styles.profitItem}>
                      <Text style={[styles.profitLabel, { color: theme.text.secondary }]}>Profit:</Text>
                      <Text style={[
                        styles.profitValue,
                        profit < 0 ? styles.negativeProfit : styles.positiveProfit
                      ]}>
                        ₹{profit.toFixed(2)}
                      </Text>
                    </View>

                    <View style={styles.profitItem}>
                      <Text style={[styles.profitLabel, { color: theme.text.secondary }]}>Margin:</Text>
                      <Text style={[
                        styles.profitValue,
                        margin < 0 ? styles.negativeProfit : styles.positiveProfit
                      ]}>
                        {margin.toFixed(2)}%
                      </Text>
                    </View>

                    <TouchableOpacity
                      style={[styles.suggestButton, { backgroundColor: theme.accent }]}
                      onPress={() => suggestPrice(item)}
                    >
                      <MaterialIcons name="lightbulb" size={16} color="white" />
                      <Text style={styles.suggestButtonText}>Suggest</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              );
            }}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={['#4CAF50']}
              />
            }
            ListEmptyComponent={
              <View style={[styles.emptyContainer, { backgroundColor: theme.surface }]}>
                <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No products found</Text>
                <TouchableOpacity
                  style={[styles.addButton, { backgroundColor: theme.primary }]}
                  onPress={() => navigation.navigate('Add Recipe')}
                >
                  <Text style={styles.addButtonText}>Add Your First Recipe</Text>
                </TouchableOpacity>
              </View>
            }
          />
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
    textAlign: 'center'
  },
  summaryBox: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#e8f5e9',
    marginBottom: 16,
    elevation: 1,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#2e7d32',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#555',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2e7d32',
  },
  card: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 1,
  },
  productTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  costLabel: {
    fontSize: 15,
    color: '#555',
  },
  costValue: {
    fontSize: 15,
    fontWeight: '500',
    color: '#555',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#f5f5f5',
    padding: 10,
    borderRadius: 6,
  },
  priceLabel: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
  },
  priceInput: {
    width: 100,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#fff',
    textAlign: 'center',
  },
  profitRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  profitItem: {
    alignItems: 'center',
  },
  profitLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  profitValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  positiveProfit: {
    color: '#4caf50',
  },
  negativeProfit: {
    color: '#f44336',
  },
  suggestButton: {
    backgroundColor: '#ff9800',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  suggestButtonText: {
    color: 'white',
    marginLeft: 4,
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

import React, { useContext } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { MaterialIcons } from '@expo/vector-icons';
import { AppContext } from '../AppContext';

import AddProductScreen from './AddProductScreen';
import RecordProductionScreen from './RecordProductionScreen';
import DashboardScreen from './DashboardScreen';
import StockManagerScreen from './StockManagerScreen';
import ProductionHistoryScreen from './ProductionHistoryScreen';
import ProductPricingScreen from './ProductPricingScreen';
import IngredientPricesScreen from './IngredientPricesScreen';
import IngredientManagerScreen from './IngredientManagerScreen';
import BillCaptureScreen from './BillCaptureScreen';
import ViewBillsScreen from './ViewBillsScreen';
import BillingStack from './BillingStack';
import AnalyticsHistoryScreen from './AnalyticsHistoryScreen';

import FirebaseSetupScreen from './FirebaseSetupScreen';
import RemoteConfigScreen from './RemoteConfigScreen';
import useProductionCount from './useProductionCount';
import useLowStockCount from './useLowStockCount';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Stack navigator for Bills screens
function BillsStack() {
  const { theme } = useContext(AppContext);
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen name="View Bills" component={ViewBillsScreen} />
      <Stack.Screen name="Capture Bill" component={BillCaptureScreen} />
    </Stack.Navigator>
  );
}



// Stack navigator for Inventory screens
function InventoryStack() {
  const { theme } = useContext(AppContext);
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.secondary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen name="Stock Manager" component={StockManagerScreen} />
      <Stack.Screen name="Ingredient Prices" component={IngredientPricesScreen} />
      <Stack.Screen name="Ingredient Manager" component={IngredientManagerScreen} />
    </Stack.Navigator>
  );
}

// Stack navigator for Recipes screens
function RecipesStack() {
  const { theme } = useContext(AppContext);
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen name="Recipes List" component={AddProductScreen} />
      <Stack.Screen name="Add Recipe" component={AddProductScreen} />
    </Stack.Navigator>
  );
}

// Stack navigator for Dashboard screens
function DashboardStack() {
  const { theme } = useContext(AppContext);
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen name="DashboardMain" component={DashboardScreen} options={{ title: 'Dashboard' }} />
      <Stack.Screen name="AnalyticsHistory" component={AnalyticsHistoryScreen} options={{ title: 'Analytics History' }} />

      <Stack.Screen name="FirebaseSetup" component={FirebaseSetupScreen} options={{ title: 'Firebase Setup' }} />
      <Stack.Screen name="RemoteConfig" component={RemoteConfigScreen} options={{ title: 'Remote Config' }} />
    </Stack.Navigator>
  );
}

export default function MainTabs() {
  const productionCount = useProductionCount();
  const lowStock = useLowStockCount();
  const { theme, userRole } = useContext(AppContext);

  // Staff can only access billing features
  const isStaff = userRole === 'staff';

  // If staff, only show billing screens
  if (isStaff) {
    return (
      <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: theme.primary,
          tabBarInactiveTintColor: theme.text.disabled,
          tabBarStyle: { backgroundColor: theme.surface },
          headerStyle: { backgroundColor: theme.primary },
          headerTintColor: '#fff',
        }}
      >
        <Tab.Screen
          name="Billing"
          component={BillingStack}
          options={{
            headerShown: false,
            tabBarIcon: ({ color, size }) => (
              <MaterialIcons name="point-of-sale" size={size} color={color} />
            ),
          }}
        />
      </Tab.Navigator>
    );
  }

  // Admin has access to all screens
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: theme.primary,
        tabBarInactiveTintColor: theme.text.disabled,
        tabBarStyle: { backgroundColor: theme.surface },
        headerStyle: { backgroundColor: theme.primary },
        headerTintColor: '#fff',
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardStack}
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="dashboard" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Recipes"
        component={RecipesStack}
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="menu-book" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Production"
        component={RecordProductionScreen}
        options={{
          tabBarBadge: productionCount > 0 ? productionCount : undefined,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="bakery-dining" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Inventory"
        component={InventoryStack}
        options={{
          headerShown: false,
          tabBarBadge: lowStock > 0 ? lowStock : undefined,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="inventory" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Pricing"
        component={ProductPricingScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="attach-money" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Bills"
        component={BillsStack}
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="receipt" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="History"
        component={ProductionHistoryScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="history" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Billing"
        component={BillingStack}
        options={{
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons name="point-of-sale" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

import React, { createContext, useState, useEffect, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { lightTheme, darkTheme } from './theme';

export const AppContext = createContext();

export default function AppProvider({ children }) {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userRole, setUserRole] = useState('admin'); // 'admin' or 'staff'
  const [user, setUser] = useState(null);

  // Memoized theme object
  const theme = useMemo(() => {
    return isDarkMode ? darkTheme : lightTheme;
  }, [isDarkMode]);

  // Load persisted settings
  useEffect(() => {
    const loadSettings = async () => {
      const themeValue = await AsyncStorage.getItem('theme');
      if (themeValue === 'dark') setIsDarkMode(true);

      const loggedIn = await AsyncStorage.getItem('loggedIn');
      const role = await AsyncStorage.getItem('userRole');
      const userData = await AsyncStorage.getItem('userData');

      if (loggedIn === 'true') {
        setIsLoggedIn(true);
        setUserRole(role || 'staff');
        setUser(userData ? JSON.parse(userData) : null);
      }
    };

    loadSettings();
  }, []);

  const toggleTheme = async () => {
    const newValue = !isDarkMode;
    setIsDarkMode(newValue);
    await AsyncStorage.setItem('theme', newValue ? 'dark' : 'light');
  };

  const login = async (email, password) => {
    // Basic hardcoded login logic for demonstration
    if (email === '<EMAIL>' && password === 'admin123') {
      await AsyncStorage.multiSet([
        ['loggedIn', 'true'],
        ['userRole', 'admin'],
        ['userData', JSON.stringify({ email, role: 'admin' })]
      ]);
      setIsLoggedIn(true);
      setUserRole('admin');
      setUser({ email, role: 'admin' });
      return { success: true };
    }

    if (email === '<EMAIL>' && password === 'staff123') {
      await AsyncStorage.multiSet([
        ['loggedIn', 'true'],
        ['userRole', 'staff'],
        ['userData', JSON.stringify({ email, role: 'staff' })]
      ]);
      setIsLoggedIn(true);
      setUserRole('staff');
      setUser({ email, role: 'staff' });
      return { success: true };
    }

    return { success: false, error: 'Invalid email or password' };
  };

  const logout = async () => {
    await AsyncStorage.multiRemove(['loggedIn', 'userRole', 'userData']);
    setIsLoggedIn(false);
    setUserRole('staff');
    setUser(null);
  };

  const register = async (email, password, name, role = 'staff') => {
    const userData = { email, name, role };
    await AsyncStorage.multiSet([
      ['loggedIn', 'true'],
      ['userRole', role],
      ['userData', JSON.stringify(userData)]
    ]);
    setIsLoggedIn(true);
    setUserRole(role);
    setUser(userData);
    return { success: true };
  };

  return (
    <AppContext.Provider
      value={{
        isDarkMode,
        toggleTheme,
        isLoggedIn,
        login,
        logout,
        theme,
        userRole,
        setUserRole,
        user,
        setUser,
        register
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

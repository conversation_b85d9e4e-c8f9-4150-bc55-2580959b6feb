import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppContext } from '../AppContext';
import { useNavigation } from '@react-navigation/native';
import AnalyticsExportService from '../utils/AnalyticsExportService';

export default function AnalyticsHistoryScreen() {
  const [loading, setLoading] = useState(true);
  const [history, setHistory] = useState([]);
  const { theme } = useContext(AppContext);
  const navigation = useNavigation();

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const historyData = await AsyncStorage.getItem('analytics_history');
      if (historyData) {
        const parsedHistory = JSON.parse(historyData);
        // Sort by date (newest first)
        parsedHistory.sort((a, b) => new Date(b.date) - new Date(a.date));
        setHistory(parsedHistory);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error loading analytics history:', error);
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return `₹${parseFloat(amount).toFixed(2)}`;
  };

  const clearHistory = async () => {
    Alert.alert(
      'Clear History',
      'Are you sure you want to clear all analytics history? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('analytics_history');
              setHistory([]);
              Alert.alert('Success', 'Analytics history has been cleared.');
            } catch (error) {
              console.error('Error clearing history:', error);
              Alert.alert('Error', 'Failed to clear analytics history.');
            }
          }
        }
      ]
    );
  };

  const renderHistoryItem = ({ item }) => (
    <View style={[styles.historyCard, { backgroundColor: theme.surface }]}>
      <View style={styles.historyHeader}>
        <Text style={[styles.historyDate, { color: theme.text.primary }]}>
          {formatDate(item.date)}
        </Text>
      </View>

      <View style={styles.metricsContainer}>
        <View style={styles.metricRow}>
          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: theme.text.secondary }]}>Products</Text>
            <Text style={[styles.metricValue, { color: theme.text.primary }]}>
              {item.totalProducts || 0}
            </Text>
          </View>

          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: theme.text.secondary }]}>Revenue</Text>
            <Text style={[styles.metricValue, { color: theme.success }]}>
              {formatCurrency(item.totalRevenue || 0)}
            </Text>
          </View>
        </View>

        <View style={styles.metricRow}>
          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: theme.text.secondary }]}>Cost</Text>
            <Text style={[styles.metricValue, { color: theme.error }]}>
              {formatCurrency(item.totalCost || 0)}
            </Text>
          </View>

          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: theme.text.secondary }]}>Profit</Text>
            <Text style={[styles.metricValue, { color: theme.primary }]}>
              {formatCurrency(item.totalProfit || 0)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.text.primary }]}>Analytics History</Text>
          <View style={styles.headerButtons}>
            {history.length > 0 && (
              <>
                <TouchableOpacity
                  style={[styles.headerButton, { backgroundColor: theme.accent }]}
                  onPress={async () => {
                    try {
                      // Get shop profile
                      const shopProfileData = await AsyncStorage.getItem('shop_profile');
                      const shopProfile = shopProfileData ? JSON.parse(shopProfileData) : null;

                      // Prepare export data
                      const exportData = {
                        totalProducts: history.reduce((sum, item) => sum + (item.totalProducts || 0), 0),
                        totalRevenue: history.reduce((sum, item) => sum + (item.totalRevenue || 0), 0),
                        totalCost: history.reduce((sum, item) => sum + (item.totalCost || 0), 0),
                        totalProfit: history.reduce((sum, item) => sum + (item.totalProfit || 0), 0),
                        history: history
                      };

                      // Export to PDF
                      const result = await AnalyticsExportService.exportToPDF(
                        exportData,
                        shopProfile,
                        'Complete History'
                      );

                      if (result.success) {
                        Alert.alert('Success', result.message || 'Analytics history exported successfully');
                      } else {
                        Alert.alert('Error', result.message || 'Failed to export analytics history');
                      }
                    } catch (error) {
                      console.error('Error exporting analytics history:', error);
                      Alert.alert('Error', 'Failed to export analytics history');
                    }
                  }}
                >
                  <MaterialIcons name="picture-as-pdf" size={18} color="white" />
                  <Text style={styles.headerButtonText}>Export</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.clearButton, { borderColor: theme.error }]}
                  onPress={clearHistory}
                >
                  <MaterialIcons name="delete" size={18} color={theme.error} />
                  <Text style={[styles.clearButtonText, { color: theme.error }]}>Clear</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>
              Loading analytics history...
            </Text>
          </View>
        ) : history.length === 0 ? (
          <View style={styles.emptyContainer}>
            <MaterialIcons name="analytics" size={64} color={theme.text.disabled} />
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>
              No analytics history found
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.text.hint }]}>
              Analytics are saved daily at midnight
            </Text>
          </View>
        ) : (
          <FlatList
            data={history}
            renderItem={renderHistoryItem}
            keyExtractor={(item, index) => `history-${index}-${item.date}`}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
  },
  headerButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
  },
  clearButtonText: {
    marginLeft: 4,
    fontWeight: '500',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  historyCard: {
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  historyHeader: {
    marginBottom: 12,
  },
  historyDate: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  metricsContainer: {
    marginTop: 8,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metric: {
    flex: 1,
  },
  metricLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

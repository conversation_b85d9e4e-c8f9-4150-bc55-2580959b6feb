import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Linking,
  Switch
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import firebaseService, { getFirebaseConfig, setFirebaseConfig } from '../utils/firebaseService';
import { AppContext } from '../AppContext';

export default function FirebaseSetupScreen() {
  const [loading, setLoading] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [authDomain, setAuthDomain] = useState('');
  const [projectId, setProjectId] = useState('');
  const [storageBucket, setStorageBucket] = useState('');
  const [messagingSenderId, setMessagingSenderId] = useState('');
  const [appId, setAppId] = useState('');
  const [databaseURL, setDatabaseURL] = useState('');
  const [useDefaultConfig, setUseDefaultConfig] = useState(true);
  const [status, setStatus] = useState('Not configured');
  const [statusColor, setStatusColor] = useState('#999');
  const navigation = useNavigation();
  const { theme } = React.useContext(AppContext);

  useEffect(() => {
    loadSavedSettings();
  }, []);

  const loadSavedSettings = async () => {
    try {
      setLoading(true);
      const config = await getFirebaseConfig();

      if (config) {
        setApiKey(config.apiKey || '');
        setAuthDomain(config.authDomain || '');
        setProjectId(config.projectId || '');
        setStorageBucket(config.storageBucket || '');
        setMessagingSenderId(config.messagingSenderId || '');
        setAppId(config.appId || '');
        setDatabaseURL(config.databaseURL || '');

        // Check if using default config
        const defaultConfig = {
          apiKey: "AIzaSyBVlgHXvbZVIGXBPYlBIiQZG9JPkKgGxSk",
          authDomain: "bakery-tracker-app.firebaseapp.com",
          projectId: "bakery-tracker-app",
          storageBucket: "bakery-tracker-app.appspot.com",
          messagingSenderId: "123456789012",
          appId: "1:123456789012:web:abcdef1234567890abcdef",
          databaseURL: "https://bakery-tracker-app-default-rtdb.firebaseio.com"
        };

        setUseDefaultConfig(
          config.apiKey === defaultConfig.apiKey &&
          config.projectId === defaultConfig.projectId
        );

        setStatus('Configured');
        setStatusColor('#4CAF50');
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading saved settings:', error);
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (useDefaultConfig) {
      // Use default config
      const defaultConfig = {
        apiKey: "AIzaSyAhcxemA28Jm6Ln3405b6zHh262du_1onw",
        authDomain: "bakery-tracker-35cc4.firebaseapp.com",
        databaseURL: "https://bakery-tracker-35cc4-default-rtdb.asia-southeast1.firebasedatabase.app",
        projectId: "bakery-tracker-35cc4",
        storageBucket: "bakery-tracker-35cc4.firebasestorage.app",
        messagingSenderId: "288727345133",
        appId: "1:288727345133:web:9aa4e3803156523f9ba9e4",
        measurementId: "G-HQ2HWRXBX9"
      };

      try {
        setLoading(true);

        // Save settings
        await setFirebaseConfig(defaultConfig);

        setStatus('Saved (Default)');
        setStatusColor('#4CAF50');

        Alert.alert(
          'Success',
          'Firebase settings saved successfully. Using default configuration.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );

        setLoading(false);
      } catch (error) {
        console.error('Error saving settings:', error);
        setStatus('Error');
        setStatusColor('#F44336');
        setLoading(false);
        Alert.alert('Error', 'Failed to save settings: ' + error.message);
      }

      return;
    }

    // Validate custom config
    if (!apiKey || !projectId || !databaseURL) {
      Alert.alert('Error', 'API Key, Project ID, and Database URL are required');
      return;
    }

    try {
      setLoading(true);

      // Create config object
      const config = {
        apiKey,
        authDomain,
        projectId,
        storageBucket,
        messagingSenderId,
        appId,
        databaseURL
      };

      // Save settings
      await setFirebaseConfig(config);

      setStatus('Saved (Custom)');
      setStatusColor('#4CAF50');

      Alert.alert(
        'Success',
        'Firebase settings saved successfully. Using custom configuration.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );

      setLoading(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      setStatus('Error');
      setStatusColor('#F44336');
      setLoading(false);
      Alert.alert('Error', 'Failed to save settings: ' + error.message);
    }
  };

  const testConnection = async () => {
    try {
      setLoading(true);
      setStatus('Testing...');
      setStatusColor('#2196F3');

      // Save settings temporarily for the test
      if (useDefaultConfig) {
        // Use default config
        const defaultConfig = {
          apiKey: "AIzaSyAhcxemA28Jm6Ln3405b6zHh262du_1onw",
          authDomain: "bakery-tracker-35cc4.firebaseapp.com",
          databaseURL: "https://bakery-tracker-35cc4-default-rtdb.asia-southeast1.firebasedatabase.app",
          projectId: "bakery-tracker-35cc4",
          storageBucket: "bakery-tracker-35cc4.firebasestorage.app",
          messagingSenderId: "288727345133",
          appId: "1:288727345133:web:9aa4e3803156523f9ba9e4",
          measurementId: "G-HQ2HWRXBX9"
        };

        await setFirebaseConfig(defaultConfig);
      } else {
        // Create config object
        const config = {
          apiKey,
          authDomain,
          projectId,
          storageBucket,
          messagingSenderId,
          appId,
          databaseURL
        };

        await setFirebaseConfig(config);
      }

      // Test the connection by initializing Firebase
      const database = await firebaseService.initializeFirebase();

      if (database) {
        setStatus('Connected');
        setStatusColor('#4CAF50');
        Alert.alert('Success', 'Connected to Firebase successfully');
      } else {
        setStatus('Connection Failed');
        setStatusColor('#F44336');
        Alert.alert('Error', 'Failed to connect to Firebase');
      }

      setLoading(false);
    } catch (error) {
      console.error('Error testing connection:', error);
      setStatus('Error');
      setStatusColor('#F44336');
      setLoading(false);
      Alert.alert('Error', 'Failed to test connection: ' + error.message);
    }
  };

  const createCollections = async () => {
    try {
      setLoading(true);
      setStatus('Creating collections...');
      setStatusColor('#2196F3');

      // Create test documents in each collection
      const collections = [
        'products',
        'ingredients',
        'productions',
        'analytics',
        'bills',
        'ingredientBills',
        'shopProfiles',
        'users'
      ];

      for (const collection of collections) {
        await firebaseService.createDocument(collection, {
          name: `Test ${collection} (Auto-created)`,
          description: `This is a test document to ensure the ${collection} collection exists`,
          isTest: true
        });
      }

      setStatus('Collections created');
      setStatusColor('#4CAF50');
      Alert.alert('Success', 'Firebase collections created successfully');

      setLoading(false);
    } catch (error) {
      console.error('Error creating collections:', error);
      setStatus('Error creating collections');
      setStatusColor('#F44336');
      setLoading(false);
      Alert.alert('Error', 'Failed to create collections: ' + error.message);
    }
  };

  const resetSettings = async () => {
    Alert.alert(
      'Confirm Reset',
      'Are you sure you want to reset the Firebase settings?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          onPress: async () => {
            try {
              setLoading(true);

              // Reset to default config
              const defaultConfig = {
                apiKey: "AIzaSyAhcxemA28Jm6Ln3405b6zHh262du_1onw",
                authDomain: "bakery-tracker-35cc4.firebaseapp.com",
                databaseURL: "https://bakery-tracker-35cc4-default-rtdb.asia-southeast1.firebasedatabase.app",
                projectId: "bakery-tracker-35cc4",
                storageBucket: "bakery-tracker-35cc4.firebasestorage.app",
                messagingSenderId: "288727345133",
                appId: "1:288727345133:web:9aa4e3803156523f9ba9e4",
                measurementId: "G-HQ2HWRXBX9"
              };

              await setFirebaseConfig(defaultConfig);

              setApiKey(defaultConfig.apiKey);
              setAuthDomain(defaultConfig.authDomain);
              setProjectId(defaultConfig.projectId);
              setStorageBucket(defaultConfig.storageBucket);
              setMessagingSenderId(defaultConfig.messagingSenderId);
              setAppId(defaultConfig.appId);
              setDatabaseURL(defaultConfig.databaseURL);
              setUseDefaultConfig(true);

              setStatus('Reset to Default');
              setStatusColor('#4CAF50');
              setLoading(false);

              Alert.alert('Success', 'Firebase settings reset to default configuration');
            } catch (error) {
              console.error('Error resetting settings:', error);
              setLoading(false);
              Alert.alert('Error', 'Failed to reset settings');
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor="#4CAF50" barStyle="light-content" />
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={24} color={theme.text.primary} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: theme.text.primary }]}>Firebase Setup</Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4CAF50" />
            <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading...</Text>
          </View>
        ) : (
          <ScrollView style={styles.content}>
            <View style={[styles.card, { backgroundColor: theme.surface }]}>
              <Text style={[styles.cardTitle, { color: theme.text.primary }]}>Firebase Settings</Text>

              <View style={styles.switchContainer}>
                <Text style={[styles.switchLabel, { color: theme.text.primary }]}>Use Default Configuration</Text>
                <Switch
                  value={useDefaultConfig}
                  onValueChange={setUseDefaultConfig}
                  trackColor={{ false: '#767577', true: '#81b0ff' }}
                  thumbColor={useDefaultConfig ? '#2196F3' : '#f4f3f4'}
                />
              </View>

              {!useDefaultConfig && (
                <>
                  <Text style={[styles.label, { color: theme.text.secondary }]}>API Key</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="AIzaSyBVlgHXvbZVIGXBPYlBIiQZG9JPkKgGxSk"
                    placeholderTextColor={theme.text.hint}
                    value={apiKey}
                    onChangeText={setApiKey}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <Text style={[styles.label, { color: theme.text.secondary }]}>Auth Domain</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="your-project-id.firebaseapp.com"
                    placeholderTextColor={theme.text.hint}
                    value={authDomain}
                    onChangeText={setAuthDomain}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <Text style={[styles.label, { color: theme.text.secondary }]}>Project ID</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="your-project-id"
                    placeholderTextColor={theme.text.hint}
                    value={projectId}
                    onChangeText={setProjectId}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <Text style={[styles.label, { color: theme.text.secondary }]}>Storage Bucket</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="your-project-id.appspot.com"
                    placeholderTextColor={theme.text.hint}
                    value={storageBucket}
                    onChangeText={setStorageBucket}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <Text style={[styles.label, { color: theme.text.secondary }]}>Messaging Sender ID</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="123456789012"
                    placeholderTextColor={theme.text.hint}
                    value={messagingSenderId}
                    onChangeText={setMessagingSenderId}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <Text style={[styles.label, { color: theme.text.secondary }]}>App ID</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="1:123456789012:web:abcdef1234567890abcdef"
                    placeholderTextColor={theme.text.hint}
                    value={appId}
                    onChangeText={setAppId}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <Text style={[styles.label, { color: theme.text.secondary }]}>Database URL</Text>
                  <TextInput
                    style={[styles.input, {
                      borderColor: theme.border,
                      backgroundColor: theme.input.background,
                      color: theme.text.primary
                    }]}
                    placeholder="https://your-project-id-default-rtdb.firebaseio.com"
                    placeholderTextColor={theme.text.hint}
                    value={databaseURL}
                    onChangeText={setDatabaseURL}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </>
              )}

              <View style={styles.statusContainer}>
                <Text style={[styles.statusLabel, { color: theme.text.secondary }]}>Status:</Text>
                <Text style={[styles.statusValue, { color: statusColor }]}>
                  {status}
                </Text>
              </View>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.testButton]}
                  onPress={testConnection}
                  disabled={loading}
                >
                  <MaterialIcons name="wifi-tethering" size={20} color="white" />
                  <Text style={styles.buttonText}>Test Connection</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.button, styles.saveButton]}
                  onPress={saveSettings}
                  disabled={loading}
                >
                  <MaterialIcons name="save" size={20} color="white" />
                  <Text style={styles.buttonText}>Save Settings</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.button, styles.createButton]}
                  onPress={createCollections}
                  disabled={loading}
                >
                  <MaterialIcons name="create-new-folder" size={20} color="white" />
                  <Text style={styles.buttonText}>Create Collections</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.button, styles.resetButton]}
                  onPress={resetSettings}
                  disabled={loading}
                >
                  <MaterialIcons name="refresh" size={20} color="white" />
                  <Text style={styles.buttonText}>Reset</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={[styles.card, { backgroundColor: theme.surface }]}>
              <Text style={[styles.cardTitle, { color: theme.text.primary }]}>How to Set Up Firebase</Text>

              <Text style={[styles.helpText, { color: theme.text.secondary }]}>
                <Text style={[styles.helpTextBold, { color: theme.text.primary }]}>Option 1: Use Default Configuration (Recommended)</Text>{'\n'}
                1. Keep "Use Default Configuration" enabled{'\n'}
                2. Click "Test Connection" to verify{'\n'}
                3. Click "Save Settings" to save{'\n'}
                4. Click "Create Collections" to create the necessary collections{'\n\n'}

                <Text style={[styles.helpTextBold, { color: theme.text.primary }]}>Option 2: Create Your Own Firebase Project</Text>{'\n'}
                1. Go to the Firebase Console (firebase.google.com){'\n'}
                2. Create a new project{'\n'}
                3. Add a web app to your project{'\n'}
                4. Copy the Firebase configuration{'\n'}
                5. Disable "Use Default Configuration"{'\n'}
                6. Paste the configuration values in the fields above{'\n'}
                7. Click "Test Connection" to verify{'\n'}
                8. Click "Save Settings" to save{'\n'}
                9. Click "Create Collections" to create the necessary collections{'\n\n'}

                <Text style={[styles.helpTextBold, { color: theme.text.primary }]}>Note:</Text>{'\n'}
                The default configuration uses a shared Firebase project for testing purposes. For production use, it's recommended to create your own Firebase project.
              </Text>

              <TouchableOpacity
                style={[styles.button, styles.firebaseButton]}
                onPress={() => Linking.openURL('https://console.firebase.google.com/')}
              >
                <MaterialIcons name="open-in-new" size={20} color="white" />
                <Text style={styles.buttonText}>Open Firebase Console</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusLabel: {
    fontSize: 16,
    marginRight: 8,
  },
  statusValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'column',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  testButton: {
    backgroundColor: '#2196F3',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  createButton: {
    backgroundColor: '#FF9800',
  },
  resetButton: {
    backgroundColor: '#F44336',
  },
  firebaseButton: {
    backgroundColor: '#FFA000',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  helpTextBold: {
    fontWeight: 'bold',
  }
});

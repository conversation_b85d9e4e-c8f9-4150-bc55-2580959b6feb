{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowJs": true, "checkJs": false, "jsx": "react-native", "target": "esnext", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@components/*": ["components/*"], "@screens/*": ["screens/*"], "@utils/*": ["utils/*"], "@assets/*": ["assets/*"], "@firebase": ["firebase.js"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}
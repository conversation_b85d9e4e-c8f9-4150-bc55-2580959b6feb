import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '../AppContext';
import remoteConfigService from '../utils/remoteConfigService';

export default function RemoteConfigScreen() {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [configValues, setConfigValues] = useState({});
  const navigation = useNavigation();
  const { theme } = useContext(AppContext);

  useEffect(() => {
    loadConfigValues();
  }, []);

  const loadConfigValues = async () => {
    try {
      setLoading(true);
      const values = await remoteConfigService.getAllConfigValues();
      setConfigValues(values);
      setLoading(false);
    } catch (error) {
      console.error('Error loading config values:', error);
      setLoading(false);
      Alert.alert('Error', 'Failed to load remote config values');
    }
  };

  const refreshConfig = async () => {
    try {
      setRefreshing(true);
      const result = await remoteConfigService.refreshConfig();
      
      if (result.success) {
        const values = await remoteConfigService.getAllConfigValues();
        setConfigValues(values);
        Alert.alert('Success', 'Remote config refreshed successfully');
      } else {
        Alert.alert('Error', result.error || 'Failed to refresh remote config');
      }
      
      setRefreshing(false);
    } catch (error) {
      console.error('Error refreshing config:', error);
      setRefreshing(false);
      Alert.alert('Error', 'Failed to refresh remote config');
    }
  };

  const renderConfigValue = (key, value) => {
    // Format the key for display
    const formattedKey = key
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
    
    // Render different UI based on value type
    if (typeof value === 'boolean') {
      return (
        <View key={key} style={[styles.configItem, { borderBottomColor: theme.border }]}>
          <Text style={[styles.configName, { color: theme.text.primary }]}>{formattedKey}</Text>
          <Switch
            value={value}
            disabled={true}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={value ? theme.primary : '#f4f3f4'}
          />
        </View>
      );
    } else if (typeof value === 'number') {
      return (
        <View key={key} style={[styles.configItem, { borderBottomColor: theme.border }]}>
          <Text style={[styles.configName, { color: theme.text.primary }]}>{formattedKey}</Text>
          <Text style={[styles.configValue, { color: theme.text.secondary }]}>{value}</Text>
        </View>
      );
    } else {
      return (
        <View key={key} style={[styles.configItem, { borderBottomColor: theme.border }]}>
          <Text style={[styles.configName, { color: theme.text.primary }]}>{formattedKey}</Text>
          <Text 
            style={[styles.configValue, { color: theme.text.secondary }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {value}
          </Text>
        </View>
      );
    }
  };

  const renderConfigSection = (title, keys) => {
    const filteredKeys = keys.filter(key => configValues[key] !== undefined);
    
    if (filteredKeys.length === 0) return null;
    
    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>{title}</Text>
        <View style={[styles.sectionContent, { backgroundColor: theme.surface }]}>
          {filteredKeys.map(key => renderConfigValue(key, configValues[key]))}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <StatusBar backgroundColor={theme.primary} barStyle="light-content" />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.text.primary} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.text.primary }]}>Remote Config</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={refreshConfig}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size="small" color={theme.primary} />
          ) : (
            <MaterialIcons name="refresh" size={24} color={theme.primary} />
          )}
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={[styles.loadingText, { color: theme.text.secondary }]}>Loading config values...</Text>
        </View>
      ) : (
        <ScrollView style={styles.content}>
          <Text style={[styles.description, { color: theme.text.secondary }]}>
            Remote Config allows you to change app behavior without updating the app. These settings are controlled from the Firebase Console.
          </Text>
          
          {renderConfigSection('App Features', [
            'enable_analytics_dashboard',
            'enable_dark_mode',
            'enable_push_notifications',
            'enable_bill_capture',
            'enable_firebase_storage',
            'enable_staff_registration',
            'enable_ingredient_management',
            'enable_production_tracking',
            'enable_billing_system',
            'enable_analytics',
            'enable_shop_profile',
            'enable_user_management'
          ])}
          
          {renderConfigSection('Firebase Services', [
            'enable_cloud_functions',
            'enable_remote_config',
            'enable_crash_reporting',
            'enable_performance_monitoring',
            'enable_dynamic_links',
            'enable_in_app_messaging',
            'enable_a_b_testing',
            'enable_cloud_messaging',
            'enable_authentication',
            'enable_realtime_database',
            'enable_cloud_storage',
            'enable_cloud_firestore',
            'enable_hosting',
            'enable_ml_kit',
            'enable_app_check'
          ])}
          
          {renderConfigSection('App Settings', [
            'min_stock_threshold_percentage',
            'daily_reset_enabled',
            'show_welcome_message',
            'welcome_message',
            'app_theme_color',
            'app_accent_color',
            'maintenance_mode',
            'maintenance_message',
            'app_version_required'
          ])}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  refreshButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionContent: {
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  configItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  configName: {
    fontSize: 16,
    flex: 1,
  },
  configValue: {
    fontSize: 16,
    fontWeight: '500',
    maxWidth: '40%',
  },
});

import React, { useContext, useEffect } from 'react';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { enableScreens } from 'react-native-screens';

import MainTabs from './screens/MainTabs';
import BillingStack from './screens/BillingStack';
import LoginScreen from './screens/LoginScreen';
import { AppContext, default as AppProvider } from './AppContext';

enableScreens();
const Stack = createNativeStackNavigator();

function AppStack() {
  const { isLoggedIn, isDarkMode, userRole } = useContext(AppContext);

  return (
    <NavigationContainer theme={isDarkMode ? DarkTheme : DefaultTheme}>
      <Stack.Navigator>
        {!isLoggedIn ? (
          <Stack.Screen name="Login" component={LoginScreen} options={{ headerShown: false }} />
        ) : userRole === 'staff' ? (
          <Stack.Screen name="Billing" component={BillingStack} options={{ headerShown: false }} />
        ) : (
          <Stack.Screen name="Main" component={MainTabs} options={{ headerShown: false }} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const checkDailyReset = async () => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const lastResetDate = await AsyncStorage.getItem('analytics_last_reset');

    if (!lastResetDate || lastResetDate !== today) {
      const currentAnalytics = await AsyncStorage.getItem('current_analytics');
      const history = await AsyncStorage.getItem('analytics_history');

      if (currentAnalytics) {
        const parsed = JSON.parse(currentAnalytics);
        parsed.date = new Date().toISOString();

        const parsedHistory = history ? JSON.parse(history) : [];
        parsedHistory.push(parsed);
        await AsyncStorage.setItem('analytics_history', JSON.stringify(parsedHistory));
      }

      const resetAnalytics = {
        totalProducts: 0,
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        date: new Date().toISOString(),
      };

      await AsyncStorage.setItem('current_analytics', JSON.stringify(resetAnalytics));
      await AsyncStorage.setItem('production_data', JSON.stringify([]));
      await AsyncStorage.setItem('analytics_last_reset', today);
    }
  } catch (error) {
    console.error('Daily reset error:', error);
  }
};

export default function App() {
  useEffect(() => {
    checkDailyReset();

    const subscription = AppState.addEventListener('change', nextState => {
      if (nextState === 'active') checkDailyReset();
    });

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <AppProvider>
          <AppStack />
        </AppProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

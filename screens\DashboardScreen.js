import React, { useRef, useEffect, useState, useContext } from 'react';
import Toast from 'react-native-root-toast';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import analyticsService from '../utils/analyticsService';
import {
  Modal,
  Pressable,
  TextInput,
  Animated,
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions
} from 'react-native';
import DashboardAnalytics from '../components/DashboardAnalytics';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import HeaderActions from './HeaderActions';
import { AppContext } from '../AppContext';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const { theme, isDarkMode, userRole } = useContext(AppContext);
  const navigation = useNavigation();
  const checkAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  // Animation values for each card
  const cardAnims = [
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current
  ];

  const [modalVisible, setModalVisible] = useState(false);
  const slideAnim = useState(new Animated.Value(300))[0];
  const [isLoading, setIsLoading] = useState(false);
  const [quickProduct, setQuickProduct] = useState('');
  const [quickQty, setQuickQty] = useState('');

  const [totalProducts, setTotalProducts] = useState(0);
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [totalCost, setTotalCost] = useState(0);
  const [profit, setProfit] = useState(0);

  // Recent production data
  const [recentProduction, setRecentProduction] = useState([]);
  const [lowStockItems, setLowStockItems] = useState([]);

  // Function to refresh dashboard data
  const refreshDashboard = () => {
    loadProductionStats();
    loadRecentProduction();
    loadLowStockItems();
  };

  // Make the refresh function available globally
  useEffect(() => {
    // Add the refresh function to the window object so it can be called from other components
    if (typeof window !== 'undefined') {
      window.refreshDashboard = refreshDashboard;
    }

    // Cleanup function
    return () => {
      if (typeof window !== 'undefined') {
        delete window.refreshDashboard;
      }
    };
  }, []);

  // Log screen view when dashboard is focused
  useFocusEffect(
    React.useCallback(() => {
      // Log screen view event
      analyticsService.logScreenView('Dashboard', 'DashboardScreen');
      return () => {};
    }, [])
  );

  useEffect(() => {
    refreshDashboard();

    // Start fade-in and scale animation for dashboard
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();

    // Staggered animation for cards
    Animated.stagger(150, [
      Animated.timing(cardAnims[0], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(cardAnims[1], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(cardAnims[2], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(cardAnims[3], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const loadProductionStats = async () => {
    const data = JSON.parse(await AsyncStorage.getItem('production_data') || '[]');
    let total = 0, cost = 0, revenue = 0;
    for (let entry of data) {
      total += entry.qty;
      cost += entry.qty * 20; // sample cost per unit
      revenue += entry.qty * 50; // sample price per unit
    }
    setTotalProducts(total);
    setTotalRevenue(revenue);
    setTotalCost(cost);
    setProfit(revenue - cost);

    // Update analytics data
    try {
      const currentAnalyticsData = await AsyncStorage.getItem('current_analytics');
      let currentAnalytics;

      if (currentAnalyticsData) {
        currentAnalytics = JSON.parse(currentAnalyticsData);
      } else {
        currentAnalytics = {
          totalProducts: 0,
          totalRevenue: 0,
          totalCost: 0,
          totalProfit: 0,
          date: new Date().toISOString()
        };
      }

      // Update with current values
      currentAnalytics.totalProducts = total;
      currentAnalytics.totalRevenue = revenue;
      currentAnalytics.totalCost = cost;
      currentAnalytics.totalProfit = revenue - cost;

      await AsyncStorage.setItem('current_analytics', JSON.stringify(currentAnalytics));
    } catch (error) {
      console.error('Error updating analytics:', error);
    }
  };

  const loadRecentProduction = async () => {
    const data = JSON.parse(await AsyncStorage.getItem('production_data') || '[]');
    // Get the 5 most recent production entries
    setRecentProduction(data.slice(0, 5));
  };

  const loadLowStockItems = async () => {
    try {
      // Get all recipes
      const keys = await AsyncStorage.getAllKeys();
      const recipeKeys = keys.filter(k => k.startsWith('recipe_'));

      const lowStockList = [];

      for (let key of recipeKeys) {
        const data = await AsyncStorage.getItem(key);
        const recipe = JSON.parse(data);

        if (recipe.ingredients) {
          recipe.ingredients.forEach(ing => {
            if (ing.stock < ing.min) {
              lowStockList.push({
                name: ing.name,
                current: ing.stock || 0,
                min: ing.min || 5,
                recipe: recipe.name
              });
            }
          });
        }
      }

      setLowStockItems(lowStockList.slice(0, 5)); // Show only top 5 low stock items
    } catch (error) {
      console.error('Error loading low stock items:', error);
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onShow={() => Animated.timing(slideAnim, { toValue: 0, duration: 300, useNativeDriver: true }).start()}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.modalContent, {
            backgroundColor: theme.surface,
            transform: [{ translateY: slideAnim }]
          }]}>
            <Text style={[styles.title, { color: theme.text.primary }]}>Quick Record</Text>
            <TextInput
              placeholder="Product"
              value={quickProduct}
              onChangeText={setQuickProduct}
              style={[styles.input, {
                borderColor: theme.border,
                backgroundColor: theme.surface,
                color: theme.text.primary
              }]}
              placeholderTextColor={theme.text.hint}
            />
            <TextInput
              placeholder="Quantity"
              keyboardType="numeric"
              value={quickQty}
              onChangeText={setQuickQty}
              style={[styles.input, {
                borderColor: theme.border,
                backgroundColor: theme.surface,
                color: theme.text.primary
              }]}
              placeholderTextColor={theme.text.hint}
            />
            <Pressable
              style={[styles.modalBtn, { backgroundColor: theme.primary }, isLoading && { backgroundColor: theme.text.disabled }]}
              onPress={async () => {
              if (isLoading) return;
              setIsLoading(true);
              if (!quickProduct || !quickQty) {
                Toast.show(`❌ Please enter product and quantity`, {
                  duration: Toast.durations.SHORT,
                  position: Toast.positions.TOP,
                  backgroundColor: 'crimson',
                  textColor: 'white',
                  shadow: true,
                  animation: true,
                  hideOnPress: true,
                });
                setIsLoading(false);
                return;
              }
              try {
                const newEntry = {
                  product: quickProduct,
                  qty: parseFloat(quickQty),
                  date: new Date().toISOString().split('T')[0],
                };
                const oldData = JSON.parse(await AsyncStorage.getItem('production_data') || '[]');
                const newData = [newEntry, ...oldData];
                await AsyncStorage.setItem('production_data', JSON.stringify(newData));
                setTotalProducts(prev => prev + newEntry.qty);
                setTotalRevenue(prev => prev + newEntry.qty * 50);
                setTotalCost(prev => prev + newEntry.qty * 20);
                setProfit(prev => prev + (newEntry.qty * 50 - newEntry.qty * 20));

                // Log production event
                analyticsService.logCustomEvent('add_production', {
                  product_name: quickProduct,
                  quantity: parseFloat(quickQty),
                  revenue: parseFloat(quickQty) * 50,
                  cost: parseFloat(quickQty) * 20
                });
                Toast.show(`${quickQty} units of ${quickProduct} saved`, {
                  duration: Toast.durations.LONG,
                  position: Toast.positions.TOP,
                  backgroundColor: '#333',
                  textColor: 'white',
                  shadow: true,
                  animation: true,
                  hideOnPress: true,
                  delay: 0,
                });
                Animated.timing(slideAnim, { toValue: 300, duration: 300, useNativeDriver: true }).start(() => setModalVisible(false));
                setQuickProduct('');
                setQuickQty('');
                setIsLoading(false);

                // Animate checkmark
                Animated.sequence([
                  Animated.timing(checkAnim, { toValue: 1, duration: 200, useNativeDriver: true }),
                  Animated.delay(1000),
                  Animated.timing(checkAnim, { toValue: 0, duration: 200, useNativeDriver: true })
                ]).start();
              } catch (error) {
                Toast.show('❌ Error saving data', {
                  duration: Toast.durations.SHORT,
                  backgroundColor: 'crimson',
                  textColor: 'white',
                  position: Toast.positions.TOP,
                });
              }
            }}>
              <Text style={{ color: '#fff', fontWeight: 'bold' }}>Save</Text>
            </Pressable>
            <Pressable
              style={[styles.modalBtn, { backgroundColor: isDarkMode ? '#555' : '#ccc' }]}
              onPress={() => setModalVisible(false)}
            >
              <Text style={{ color: theme.text.primary }}>Cancel</Text>
            </Pressable>
          </Animated.View>
        </View>
      </Modal>
      <HeaderActions />

      <Animated.View style={{
        opacity: fadeAnim,
        transform: [{ scale: scaleAnim }]
      }}>
        <Text style={[styles.title, { color: theme.text.primary }]}>Bakery Dashboard</Text>

        <View style={styles.statsContainer}>
          <Animated.View
            style={[styles.statsCard, { backgroundColor: '#e0f7fa' }, {
              opacity: cardAnims[0],
              transform: [{ translateY: cardAnims[0].interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}]
            }]}
          >
            <View style={styles.iconContainer}>
              <MaterialIcons name="inventory" size={24} color="#0097a7" />
            </View>
            <View style={styles.statsContent}>
              <Text style={styles.statsValue}>{totalProducts}</Text>
              <Text style={styles.statsLabel}>Total Products</Text>
            </View>
          </Animated.View>

          <Animated.View
            style={[styles.statsCard, { backgroundColor: '#e8f5e9' }, {
              opacity: cardAnims[1],
              transform: [{ translateY: cardAnims[1].interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}]
            }]}
          >
            <View style={styles.iconContainer}>
              <MaterialIcons name="payments" size={24} color="#43a047" />
            </View>
            <View style={styles.statsContent}>
              <Text style={styles.statsValue}>₹{totalRevenue.toFixed(0)}</Text>
              <Text style={styles.statsLabel}>Total Revenue</Text>
            </View>
          </Animated.View>
        </View>

        <View style={styles.statsContainer}>
          <Animated.View
            style={[styles.statsCard, { backgroundColor: '#ffebee' }, {
              opacity: cardAnims[2],
              transform: [{ translateY: cardAnims[2].interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}]
            }]}
          >
            <View style={styles.iconContainer}>
              <MaterialIcons name="shopping-cart" size={24} color="#e53935" />
            </View>
            <View style={styles.statsContent}>
              <Text style={styles.statsValue}>₹{totalCost.toFixed(0)}</Text>
              <Text style={styles.statsLabel}>Total Cost</Text>
            </View>
          </Animated.View>

          <Animated.View
            style={[styles.statsCard, { backgroundColor: '#fff8e1' }, {
              opacity: cardAnims[3],
              transform: [{ translateY: cardAnims[3].interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}]
            }]}
          >
            <View style={styles.iconContainer}>
              <MaterialIcons name="trending-up" size={24} color="#ffa000" />
            </View>
            <View style={styles.statsContent}>
              <Text style={styles.statsValue}>₹{profit.toFixed(0)}</Text>
              <Text style={styles.statsLabel}>Profit</Text>
            </View>
          </Animated.View>
        </View>

        {/* Recent Production Section */}
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Recent Production</Text>
          {recentProduction.length > 0 ? (
            recentProduction.map((item, index) => (
              <View key={index} style={styles.recentItem}>
                <View style={styles.recentItemIcon}>
                  <MaterialIcons name="bakery-dining" size={20} color="#4CAF50" />
                </View>
                <View style={styles.recentItemContent}>
                  <Text style={[styles.recentItemTitle, { color: theme.text.primary }]}>{item.product}</Text>
                  <Text style={[styles.recentItemSubtitle, { color: theme.text.secondary }]}>{item.date}</Text>
                </View>
                <Text style={styles.recentItemQty}>{item.qty} units</Text>
              </View>
            ))
          ) : (
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No recent production data</Text>
          )}
        </View>

        {/* Analytics Section */}
        <View style={[styles.sectionContainer, { backgroundColor: theme.card.statsYellow, padding: 0 }]}>
          <View style={styles.analyticsHeader}>
            <Text style={[styles.sectionTitle, { color: theme.text.primary, margin: 16 }]}>Analytics Dashboard</Text>
            <TouchableOpacity
              style={[styles.historyButton, { backgroundColor: theme.primary }]}
              onPress={() => navigation.navigate('Dashboard', { screen: 'AnalyticsHistory' })}
            >
              <MaterialIcons name="history" size={16} color="white" />
              <Text style={styles.historyButtonText}>History</Text>
            </TouchableOpacity>
          </View>
          <DashboardAnalytics />
        </View>

        {/* Billing System Access */}
        <View style={[styles.sectionContainer, { backgroundColor: theme.card.statsBlue }]}>
          <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Billing System</Text>
          <Text style={[styles.billingDescription, { color: theme.text.secondary }]}>
            Access the complete point-of-sale system for your bakery. Create bills, manage sales, and view sales history.
          </Text>
          <TouchableOpacity
            style={[styles.billingButton, { backgroundColor: theme.primary }]}
            onPress={() => {
              // Log navigation event
              analyticsService.logCustomEvent('navigate_to_billing');
              navigation.navigate('Billing', { screen: 'Billing System' });
            }}
          >
            <MaterialIcons name="point-of-sale" size={24} color="white" />
            <Text style={styles.billingButtonText}>Open Billing System</Text>
          </TouchableOpacity>
        </View>

        {/* Firebase Setup */}
        <View style={[styles.sectionContainer, { backgroundColor: theme.card.statsGreen }]}>
          <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Firebase Database Setup</Text>
          <Text style={[styles.billingDescription, { color: theme.text.secondary }]}>
            Configure Firebase Realtime Database to store your data in the cloud for free. This is the recommended way to ensure your data is saved and synchronized across devices.
          </Text>
          <TouchableOpacity
            style={[styles.billingButton, { backgroundColor: theme.primary }]}
            onPress={() => {
              // Log navigation event
              analyticsService.logCustomEvent('navigate_to_firebase_setup');
              navigation.navigate('Dashboard', { screen: 'FirebaseSetup' });
            }}
          >
            <MaterialIcons name="cloud-sync" size={24} color="white" />
            <Text style={styles.billingButtonText}>Configure Firebase</Text>
          </TouchableOpacity>
        </View>

        {/* Remote Config */}
        <View style={[styles.sectionContainer, { backgroundColor: theme.card.statsBlue }]}>
          <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Remote Configuration</Text>
          <Text style={[styles.billingDescription, { color: theme.text.secondary }]}>
            View and manage remote configuration settings that control app behavior without requiring app updates. These settings are managed from the Firebase Console.
          </Text>
          <TouchableOpacity
            style={[styles.billingButton, { backgroundColor: theme.primary }]}
            onPress={() => {
              // Log navigation event
              analyticsService.logCustomEvent('navigate_to_remote_config');
              navigation.navigate('Dashboard', { screen: 'RemoteConfig' });
            }}
          >
            <MaterialIcons name="settings-remote" size={24} color="white" />
            <Text style={styles.billingButtonText}>View Remote Config</Text>
          </TouchableOpacity>
        </View>

        {/* Low Stock Alert Section */}
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>Low Stock Alerts</Text>
          {lowStockItems.length > 0 ? (
            lowStockItems.map((item, index) => (
              <View key={index} style={styles.alertItem}>
                <View style={styles.alertItemIcon}>
                  <MaterialIcons name="error-outline" size={20} color="#F44336" />
                </View>
                <View style={styles.alertItemContent}>
                  <Text style={[styles.alertItemTitle, { color: theme.text.primary }]}>{item.name}</Text>
                  <Text style={[styles.alertItemSubtitle, { color: theme.text.secondary }]}>Used in {item.recipe}</Text>
                </View>
                <View style={styles.alertItemStatus}>
                  <Text style={styles.alertItemStatusText}>{item.current}/{item.min}</Text>
                </View>
              </View>
            ))
          ) : (
            <Text style={[styles.emptyText, { color: theme.text.secondary }]}>No low stock items</Text>
          )}
        </View>
      </Animated.View>
      <TouchableOpacity
        style={[styles.floatingButton, { backgroundColor: theme.primary }]}
        onPress={() => setModalVisible(true)}
      >
        {isLoading ? <Text style={{ color: 'white', fontSize: 20 }}>...</Text> : <Text style={styles.floatingText}>+</Text>}
      </TouchableOpacity>
      <Animated.View style={{
        position: 'absolute',
        top: 50,
        alignSelf: 'center',
        backgroundColor: theme.success,
        padding: 10,
        borderRadius: 30,
        opacity: checkAnim,
        transform: [{ scale: checkAnim }],
        zIndex: 10
      }}>
        <Text style={{ color: 'white', fontSize: 28 }}>✅</Text>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 80,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
    color: '#333',
    textAlign: 'center'
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statsCard: {
    width: (width - 40) / 2,
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statsContent: {
    flex: 1,
  },
  statsValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statsLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  sectionContainer: {
    borderRadius: 12,
    padding: 16,
    marginTop: 24,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  recentItemIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#e8f5e9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recentItemContent: {
    flex: 1,
  },
  recentItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  recentItemSubtitle: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  recentItemQty: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  alertItemIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#ffebee',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  alertItemContent: {
    flex: 1,
  },
  alertItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  alertItemSubtitle: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  alertItemStatus: {
    backgroundColor: '#ffebee',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  alertItemStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#F44336',
  },
  emptyText: {
    textAlign: 'center',
    color: '#888',
    fontStyle: 'italic',
    padding: 16,
  },
  floatingButton: {
    position: 'absolute',
    right: 24,
    bottom: 24,
    backgroundColor: '#1e90ff',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 6,
  },
  floatingText: {
    color: 'white',
    fontSize: 30,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 8,
    width: '80%',
    gap: 10
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 8,
    borderRadius: 6
  },
  modalBtn: {
    backgroundColor: '#1e90ff',
    padding: 10,
    borderRadius: 6,
    alignItems: 'center'
  },
  billingDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  billingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  billingButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  analyticsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 16,
  },
  historyButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
});

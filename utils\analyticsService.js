import AsyncStorage from '@react-native-async-storage/async-storage';

const logEvent = async (eventName, details = {}) => {
  const log = {
    event: eventName,
    timestamp: new Date().toISOString(),
    details
  };
  const history = JSON.parse(await AsyncStorage.getItem('event_logs')) || [];
  history.push(log);
  await AsyncStorage.setItem('event_logs', JSON.stringify(history));
};

const logScreenView = async (screenName, screenClass) => {
  await logEvent('screen_view', { screenName, screenClass });
};

export default {
  logEvent,
  logScreenView
};
